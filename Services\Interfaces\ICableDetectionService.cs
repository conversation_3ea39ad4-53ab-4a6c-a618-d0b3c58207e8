using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using MEP.TraylorSwift.Models;
using System.Collections.Generic;

namespace MEP.TraylorSwift.Services.Interfaces
{
    /// <summary>
    /// Service for detecting and analyzing cables in relation to cable trays
    /// </summary>
    public interface ICableDetectionService
    {
        #region Cable Discovery

        /// <summary>
        /// Find all electrical circuits in the document
        /// </summary>
        /// <returns>List of electrical circuits</returns>
        List<Element> FindAllElectricalCircuits();

        /// <summary>
        /// Find all electrical equipment in the document
        /// </summary>
        /// <returns>List of electrical equipment elements</returns>
        List<Element> FindAllElectricalEquipment();

        /// <summary>
        /// Create cable model from circuit information
        /// </summary>
        /// <param name="circuit">Electrical circuit element</param>
        /// <returns>Cable model or null if creation failed</returns>
        CableModel CreateCableModelFromCircuit(Element circuit);

        /// <summary>
        /// Get all cables from circuits in the document
        /// </summary>
        /// <returns>Dictionary of cables indexed by cable reference</returns>
        Dictionary<string, CableModel> GetAllCablesFromCircuits();

        #endregion

        #region Circuit Path Analysis

        /// <summary>
        /// Get circuit path geometry from electrical circuit
        /// </summary>
        /// <param name="circuit">Electrical circuit element</param>
        /// <returns>List of points representing the circuit path</returns>
        List<XYZ> GetCircuitPathGeometry(Element circuit);

        /// <summary>
        /// Get circuit path curves from electrical circuit
        /// </summary>
        /// <param name="circuit">Electrical circuit element</param>
        /// <returns>List of curves representing the circuit path</returns>
        List<Curve> GetCircuitPathCurves(Element circuit);

        /// <summary>
        /// Extract cable routing information from circuit path
        /// </summary>
        /// <param name="circuit">Electrical circuit element</param>
        /// <returns>Cable routing information</returns>
        CableRoutingInfo ExtractCableRoutingInfo(Element circuit);

        #endregion

        #region Geometric Intersection

        /// <summary>
        /// Find cables that intersect with a cable tray using geometric analysis
        /// </summary>
        /// <param name="cableTray">Cable tray to analyze</param>
        /// <param name="availableCables">Available cables to check</param>
        /// <param name="tolerance">Intersection tolerance</param>
        /// <returns>List of cables that intersect the tray</returns>
        List<CableModel> FindCablesInTray(CableTray cableTray, Dictionary<string, CableModel> availableCables, double tolerance = 0.1);

        /// <summary>
        /// Check if a cable intersects with a cable tray
        /// </summary>
        /// <param name="cable">Cable to check</param>
        /// <param name="cableTray">Cable tray to check against</param>
        /// <param name="tolerance">Intersection tolerance</param>
        /// <returns>True if cable intersects the tray</returns>
        bool DoesCableIntersectTray(CableModel cable, CableTray cableTray, double tolerance = 0.1);

        /// <summary>
        /// Find cables near a cable tray within a specified distance
        /// </summary>
        /// <param name="cableTray">Cable tray to analyze</param>
        /// <param name="availableCables">Available cables to check</param>
        /// <param name="maxDistance">Maximum distance for proximity check</param>
        /// <returns>List of cables near the tray</returns>
        List<CableModel> FindCablesNearTray(CableTray cableTray, Dictionary<string, CableModel> availableCables, double maxDistance = 1.0);

        #endregion

        #region Circuit Path Intersection

        /// <summary>
        /// Find cables by analyzing circuit path intersection with cable trays
        /// </summary>
        /// <param name="cableTray">Cable tray to analyze</param>
        /// <param name="circuits">Available electrical circuits</param>
        /// <param name="tolerance">Intersection tolerance</param>
        /// <returns>List of cables whose circuit paths intersect the tray</returns>
        List<CableModel> FindCablesByCircuitPathIntersection(CableTray cableTray, List<Element> circuits, double tolerance = 0.1);

        /// <summary>
        /// Check if circuit path intersects with cable tray
        /// </summary>
        /// <param name="circuit">Electrical circuit to check</param>
        /// <param name="cableTray">Cable tray to check against</param>
        /// <param name="tolerance">Intersection tolerance</param>
        /// <returns>True if circuit path intersects the tray</returns>
        bool DoesCircuitPathIntersectTray(Element circuit, CableTray cableTray, double tolerance = 0.1);

        #endregion

        #region Connectivity Analysis

        /// <summary>
        /// Find cables connected to specific electrical equipment
        /// </summary>
        /// <param name="equipment">Electrical equipment element</param>
        /// <returns>List of cables connected to the equipment</returns>
        List<CableModel> FindCablesConnectedToEquipment(Element equipment);

        /// <summary>
        /// Analyze connectivity between electrical equipment through cable trays
        /// </summary>
        /// <param name="fromEquipment">Source equipment</param>
        /// <param name="toEquipment">Destination equipment</param>
        /// <param name="cableTrays">Available cable trays</param>
        /// <returns>List of cable trays forming the connection path</returns>
        List<CableTray> AnalyzeEquipmentConnectivity(Element fromEquipment, Element toEquipment, List<CableTray> cableTrays);

        /// <summary>
        /// Find the shortest path between two points through cable trays
        /// </summary>
        /// <param name="startPoint">Start point</param>
        /// <param name="endPoint">End point</param>
        /// <param name="cableTrays">Available cable trays</param>
        /// <returns>List of cable trays forming the shortest path</returns>
        List<CableTray> FindShortestTrayPath(XYZ startPoint, XYZ endPoint, List<CableTray> cableTrays);

        #endregion

        #region Cable Properties

        /// <summary>
        /// Extract cable properties from circuit parameters
        /// </summary>
        /// <param name="circuit">Electrical circuit element</param>
        /// <returns>Cable properties dictionary</returns>
        Dictionary<string, object> ExtractCableProperties(Element circuit);

        /// <summary>
        /// Calculate cable diameter from circuit properties
        /// </summary>
        /// <param name="circuit">Electrical circuit element</param>
        /// <returns>Cable diameter in project units</returns>
        double CalculateCableDiameter(Element circuit);

        /// <summary>
        /// Calculate cable weight from circuit properties
        /// </summary>
        /// <param name="circuit">Electrical circuit element</param>
        /// <param name="length">Cable length</param>
        /// <returns>Cable weight in project units</returns>
        double CalculateCableWeight(Element circuit, double length);

        /// <summary>
        /// Determine cable type from circuit properties
        /// </summary>
        /// <param name="circuit">Electrical circuit element</param>
        /// <returns>Cable type classification</returns>
        CableType DetermineCableType(Element circuit);

        #endregion

        #region Validation and Quality Control

        /// <summary>
        /// Validate cable detection results
        /// </summary>
        /// <param name="cableTray">Cable tray that was analyzed</param>
        /// <param name="detectedCables">Cables that were detected</param>
        /// <returns>List of validation issues</returns>
        List<string> ValidateCableDetection(CableTray cableTray, List<CableModel> detectedCables);

        /// <summary>
        /// Check for duplicate cable assignments
        /// </summary>
        /// <param name="allTraySegments">All tray segments to check</param>
        /// <returns>List of duplicate cable assignments</returns>
        List<string> CheckForDuplicateCableAssignments(List<CableTraySegmentModel> allTraySegments);

        /// <summary>
        /// Verify cable-tray associations are geometrically valid
        /// </summary>
        /// <param name="traySegment">Tray segment to verify</param>
        /// <returns>True if all cable associations are geometrically valid</returns>
        bool VerifyCableTrayAssociations(CableTraySegmentModel traySegment);

        #endregion

        #region Advanced Analysis

        /// <summary>
        /// Perform multi-criteria cable detection combining different methods
        /// </summary>
        /// <param name="cableTray">Cable tray to analyze</param>
        /// <param name="availableCables">Available cables</param>
        /// <param name="circuits">Available circuits</param>
        /// <param name="analysisOptions">Analysis configuration options</param>
        /// <returns>Ranked list of cable detection results</returns>
        List<CableDetectionResult> PerformMultiCriteriaCableDetection(
            CableTray cableTray, 
            Dictionary<string, CableModel> availableCables, 
            List<Element> circuits,
            Models.CableDetectionOptions analysisOptions);

        /// <summary>
        /// Perform only cables that are in a tray detection
        /// </summary>
        /// <param name="cableTray">Cable tray to analyze</param>
        /// <param name="availableCables">Available cables</param>
        /// <param name="analysisOptions">Analysis configuration options</param>
        /// <returns>Ranked list of cable detection results</returns>
        List<CableDetectionResult> PerformCablesInTrayDetection(
            CableTray cableTray,
            Dictionary<string, CableModel> availableCables,
            Models.CableDetectionOptions analysisOptions); 

        /// <summary>
        /// Analyze cable routing efficiency through tray network
        /// </summary>
        /// <param name="cable">Cable to analyze</param>
        /// <param name="trayNetwork">Network of cable trays</param>
        /// <returns>Routing efficiency analysis results</returns>
        RoutingEfficiencyResult AnalyzeCableRoutingEfficiency(CableModel cable, List<CableTraySegmentModel> trayNetwork);

        #endregion
    }

    /// <summary>
    /// Cable routing information extracted from circuit
    /// </summary>
    public class CableRoutingInfo
    {
        public List<XYZ> RoutePoints { get; set; }
        public List<Curve> RouteCurves { get; set; }
        public double TotalLength { get; set; }
        public BoundingBoxXYZ RouteBoundingBox { get; set; }
        public Element FromEquipment { get; set; }
        public Element ToEquipment { get; set; }

        public CableRoutingInfo()
        {
            RoutePoints = new List<XYZ>();
            RouteCurves = new List<Curve>();
        }
    }

    /// <summary>
    /// Cable detection result with confidence score
    /// </summary>
    public class CableDetectionResult
    {
        public CableModel Cable { get; set; }
        public double ConfidenceScore { get; set; }
        public GeometricAnalysisType AnalysisMethod { get; set; }
        public string Reason { get; set; }
        public Dictionary<string, object> AdditionalData { get; set; }

        public CableDetectionResult()
        {
            AdditionalData = new Dictionary<string, object>();
        }
    }



    /// <summary>
    /// Routing efficiency analysis result
    /// </summary>
    public class RoutingEfficiencyResult
    {
        public double EfficiencyScore { get; set; }
        public double ActualLength { get; set; }
        public double OptimalLength { get; set; }
        public List<string> ImprovementSuggestions { get; set; }
        public List<CableTraySegmentModel> AlternativeRoute { get; set; }

        public RoutingEfficiencyResult()
        {
            ImprovementSuggestions = new List<string>();
            AlternativeRoute = new List<CableTraySegmentModel>();
        }
    }
}
