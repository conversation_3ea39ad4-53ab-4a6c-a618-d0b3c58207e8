using MEP.TraylorSwift.ViewModels;
using System.Windows;

namespace MEP.TraylorSwift.Views
{
    /// <summary>
    /// Interaction logic for CableViewerWindow.xaml
    /// </summary>
    public partial class CableViewerWindow : Window
    {
        #region Fields

        private CableViewerViewModel _viewModel;

        #endregion

        #region Properties

        /// <summary>
        /// The ViewModel for this window
        /// </summary>
        public CableViewerViewModel ViewModel
        {
            get => _viewModel;
            set
            {
                if (_viewModel != value)
                {
                    _viewModel = value;
                    DataContext = _viewModel;
                }
            }
        }

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the CableViewerWindow
        /// </summary>
        public CableViewerWindow()
        {
            InitializeComponent();
        }

        /// <summary>
        /// Initialize the CableViewerWindow with a ViewModel
        /// </summary>
        /// <param name="viewModel">The ViewModel to use</param>
        public CableViewerWindow(CableViewerViewModel viewModel) : this()
        {
            ViewModel = viewModel;
            
            // Subscribe to close request
            if (viewModel != null)
            {
                viewModel.CloseRequested += (s, e) => Close();
            }
        }

        #endregion
    }
}
