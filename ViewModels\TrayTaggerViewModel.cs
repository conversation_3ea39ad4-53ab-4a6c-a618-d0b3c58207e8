using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.DependencyInjection;
using CommunityToolkit.Mvvm.Input;
using MEP.TraylorSwift.Handlers;
using MEP.TraylorSwift.Models;
using MEP.TraylorSwift.Services;
using MEP.TraylorSwift.Services.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows.Data;
using System.Windows.Input;

namespace MEP.TraylorSwift.ViewModels
{
    /// <summary>
    /// Main ViewModel for the Traylor Swift cable tray tagging workflow
    /// </summary>
    public partial class TrayTaggerViewModel : BaseViewModel
    {
        #region Fields

        private readonly Document _document;
        private readonly UIDocument _uIDocument;
        private readonly BecaActivityLoggerData _logger;
        private readonly IDataLoadingService _dataLoadingService;
        private readonly ITrayAnalysisService _trayAnalysisService;
        private readonly ICableDetectionService _cableDetectionService;
        private readonly ITaggingService _taggingService;
        private readonly ISectionViewService _sectionViewService;
        private readonly IParametricCableService _parametricCableService;
        private readonly NavigationService _navigationService;

        [ObservableProperty]
        private TS_Data _tSData;

        [ObservableProperty]
        private string _selectedLevel = "All";

        [ObservableProperty]
        private CableTraySegmentModel _selectedTraySegment;

        [ObservableProperty]
        private double _progressValue;

        [ObservableProperty]
        private bool _showProgressBar;

        [ObservableProperty]
        private string _currentPageTitle = "Cable Tray Segments";

        #endregion

        #region Properties

        /// <summary>
        /// Collection of cable tray segments for display
        /// </summary>
        public ObservableCollection<CableTraySegmentModel> TraySegments { get; private set; }

        /// <summary>
        /// Filtered view of tray segments based on selected level
        /// </summary>
        public ICollectionView TraySegmentsView { get; private set; }

        /// <summary>
        /// Available levels for filtering
        /// </summary>
        public ObservableCollection<string> AvailableLevels { get; private set; }

        /// <summary>
        /// Navigation service for page management
        /// </summary>
        public NavigationService NavigationService => _navigationService;

        /// <summary>
        /// Initialize navigation and navigate to default page
        /// </summary>
        public void InitializeNavigation(System.Windows.Controls.Frame frame)
        {
            try
            {
                if (_navigationService != null && frame != null)
                {
                    _navigationService.Initialize(frame);
                    NavigateToTraySegments();
                    System.Diagnostics.Debug.WriteLine("Navigation initialized from ViewModel");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ViewModel InitializeNavigation: {ex.Message}");
                _logger?.Log($"Failed to initialize navigation: {ex.Message}", LogType.Error);
            }
        }

        // Properties auto-generated by [ObservableProperty] attributes

        // Custom property change handlers for MVVM Toolkit
        partial void OnSelectedLevelChanged(string value)
        {
            FilterTraySegmentsByLevel();
        }

        partial void OnSelectedTraySegmentChanged(CableTraySegmentModel value)
        {
            OnSelectedTraySegmentChangedInternal();
        }

        /// <summary>
        /// Statistics about the loaded data
        /// </summary>
        public TS_DataStatistics DataStatistics => TSData?.Statistics;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the TrayTaggerViewModel
        /// </summary>
        public TrayTaggerViewModel(
            Document document,
            UIDocument uIDocument,
            BecaActivityLoggerData logger,
            IDataLoadingService dataLoadingService,
            ITrayAnalysisService trayAnalysisService,
            ICableDetectionService cableDetectionService,
            ITaggingService taggingService,
            ISectionViewService sectionViewService,
            IParametricCableService parametricCableService,
            IServiceProvider serviceProvider) : base(serviceProvider)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
            _uIDocument = uIDocument ?? throw new ArgumentNullException(nameof(uIDocument));
            _logger = logger;
            _dataLoadingService = dataLoadingService ?? throw new ArgumentNullException(nameof(dataLoadingService));
            _trayAnalysisService = trayAnalysisService ?? throw new ArgumentNullException(nameof(trayAnalysisService));
            _cableDetectionService = cableDetectionService ?? throw new ArgumentNullException(nameof(cableDetectionService));
            _taggingService = taggingService ?? throw new ArgumentNullException(nameof(taggingService));
            _sectionViewService = sectionViewService ?? throw new ArgumentNullException(nameof(sectionViewService));
            _parametricCableService = parametricCableService ?? throw new ArgumentNullException(nameof(parametricCableService));
            _navigationService = new NavigationService();

            // Initialize collections
            TraySegments = new ObservableCollection<CableTraySegmentModel>();
            AvailableLevels = new ObservableCollection<string>();

            // Create filtered view
            TraySegmentsView = CollectionViewSource.GetDefaultView(TraySegments);
            TraySegmentsView.Filter = FilterTraySegment;

            // Initialize with empty data
            TSData = new TS_Data(_document);

            LoadData();
        }

        #endregion

        #region Data Loading

        /// <summary>
        /// Load data from the Revit document
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanLoadData))]
        private void LoadData()
        {
            try
            {
                IsLoading = true;
                ShowProgressBar = true;
                StatusMessage = "Loading data from document...";
                ProgressValue = 0;

                TSData = _dataLoadingService.LoadTSDataWithProgress((message, progress) =>
                {
                    StatusMessage = message;
                    ProgressValue = progress * 100;
                });

                // Update UI collections
                UpdateTraySegments();
                UpdateAvailableLevels();

            }
            catch (Exception ex)
            {
                StatusMessage = $"Error loading data: {ex.Message}";
                _logger?.Log($"Failed to load data: {ex.Message}", LogType.Error);
            }
            finally
            {
                IsLoading = false;
                ShowProgressBar = false;
                ProgressValue = 0;
            }
        }

        private bool CanLoadData() => !IsLoading;

        #endregion

        #region Tray Segment Management

        /// <summary>
        /// Add a new tray segment (request user selection in Revit)
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanAddTraySegment))]
        private void AddTraySegment()
        {
            try
            {
                StatusMessage = "Requesting tray selection in Revit...";

                // Request external event for tray selection
                MakeRequest(RequestId.AddTraySegment);

                _logger?.Log("Add tray segment requested - user selection required", LogType.Information);
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error adding tray segment: {ex.Message}";
                _logger?.Log($"Failed to add tray segment: {ex.Message}", LogType.Error);
            }
        }

        private bool CanAddTraySegment() => !IsLoading;

        /// <summary>
        /// Remove a tray segment
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanRemoveTraySegment))]
        private void RemoveTraySegment(CableTraySegmentModel traySegment)
        {
            try
            {
                if (traySegment == null) return;

                StatusMessage = $"Removing tray segment {traySegment.TrayRef}...";

                // Store tray segment for processing
                SharedData.SelectedTraySegmentForProcessing = traySegment; 
                SharedData.ProcessingAction = "RemoveTraySegment";

                // Request external event
                MakeRequest(RequestId.RemoveTraySegment);

                // Remove from UI collection
                TraySegments.Remove(traySegment);
                if (TSData?.CableTraySegments != null)
                {
                    TSData.CableTraySegments.Remove(traySegment);
                }

                StatusMessage = $"Tray segment {traySegment.TrayRef} removed.";
                _logger?.Log($"Tray segment {traySegment.TrayRef} removed", LogType.Information);
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error removing tray segment: {ex.Message}";
                _logger?.Log($"Failed to remove tray segment: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Open the floor plan view containing the tray segment
        /// </summary>
        [RelayCommand]
        private void OpenFloorPlan(CableTraySegmentModel traySegment)
        {
            try
            {
                if (traySegment == null) return;

                StatusMessage = $"Opening {traySegment.FloorPlan.Name}...";

                // Store tray segment for processing
                SharedData.SelectedTraySegmentForProcessing = traySegment;
                SharedData.ProcessingAction = "OpenFLoorPlan";

                _uIDocument.ActiveView = traySegment.FloorPlan;

                _uIDocument.ShowElements(traySegment.CableTray.Id);
                _uIDocument.Selection.SetElementIds(new List<ElementId>() { traySegment.CableTray.Id });
                _uIDocument.RefreshActiveView();

                StatusMessage = $"{traySegment.FloorPlan.Name} opened.";
                _logger?.Log($"{traySegment.FloorPlan.Name} opened.", LogType.Information);
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error Opening {traySegment.FloorPlan.Name}";
                _logger?.Log($"Failed to Open {traySegment.FloorPlan.Name}", LogType.Error);
            }
        }

        private bool CanRemoveTraySegment(CableTraySegmentModel traySegment) => !IsLoading && traySegment != null;

        /// <summary>
        /// Configure a tray segment
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanConfigureTraySegment))]
        private void ConfigureTraySegment(CableTraySegmentModel traySegment)
        {
            try
            {
                if (traySegment == null) return;

                // Create configure page view model
                var sectionViewService = _serviceProvider.GetService<ISectionViewService>();
                var parametricCableService = _serviceProvider.GetService<IParametricCableService>();
                var trayAnalysisService = _serviceProvider.GetService<ITrayAnalysisService>();

                var configureViewModel = new ConfigureTraySegmentViewModel(
                    _uIDocument,
                    traySegment,
                    sectionViewService,
                    parametricCableService,
                    trayAnalysisService,
                    _serviceProvider);

                // Initialize the configure view model with the same RequestHandler and ExternalEvent
                configureViewModel.Initialize(_requestHandler, _externalEvent);

                // Subscribe to close request to navigate back
                configureViewModel.CloseRequested += (s, e) => NavigateToTraySegments();

                // Create and navigate to configure page
                var configurePage = new Views.Pages.ConfigureTraySegmentPage();
                configurePage.DataContext = configureViewModel;

                _navigationService.NavigateTo(configurePage);
                CurrentPageTitle = $"Configure: {traySegment.TrayRef}";

                StatusMessage = $"Configuring tray segment: {traySegment.TrayRef}";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error configuring tray segment: {ex.Message}";
                _logger?.Log($"Failed to configure tray segment: {ex.Message}", LogType.Error);
            }
        }

        private bool CanConfigureTraySegment(CableTraySegmentModel traySegment) => !IsLoading && traySegment != null;

        /// <summary>
        /// Open cable viewer window
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanOpenCableViewer))]
        private void OpenCableViewer()
        {
            try
            {
                if (TSData == null) return;

                // Create cable viewer page view model
                var cableViewerViewModel = new CableViewerViewModel(TSData, _serviceProvider);

                // Initialize with external event handling if needed
                cableViewerViewModel.Initialize(_requestHandler, _externalEvent);

                // Subscribe to close request to navigate back
                cableViewerViewModel.CloseRequested += (s, e) => NavigateToTraySegments();

                // Create and navigate to cable viewer page
                var cableViewerPage = new Views.Pages.CableViewerPage();
                cableViewerPage.DataContext = cableViewerViewModel;

                _navigationService.NavigateTo(cableViewerPage);
                CurrentPageTitle = "Cable Viewer";

                StatusMessage = "Cable viewer opened.";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error opening cable viewer: {ex.Message}";
                _logger?.Log($"Failed to open cable viewer: {ex.Message}", LogType.Error);
            }
        }

        private bool CanOpenCableViewer() => !IsLoading && TSData != null;

        /// <summary>
        /// Navigate to tray segments page
        /// </summary>
        [RelayCommand]
        private void NavigateToTraySegments()
        {
            try
            {
                // Ensure any pending collection view edits are cancelled before navigation
                SafeRefreshCollectionView();

                // Create and navigate to tray segments page
                var traySegmentsPage = new Views.Pages.TraySegmentsPage();
                traySegmentsPage.DataContext = this;

                _navigationService.NavigateTo(traySegmentsPage);
                CurrentPageTitle = "Cable Tray Segments";

                StatusMessage = "Navigated to tray segments.";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error navigating to tray segments: {ex.Message}";
                _logger?.Log($"Failed to navigate to tray segments: {ex.Message}", LogType.Error);
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Update the tray segments collection from TS_Data
        /// </summary>
        private void UpdateTraySegments()
        {
            TraySegments.Clear();
            if (TSData?.CableTraySegments != null)
            {
                foreach (var segment in TSData.CableTraySegments)
                {
                    TraySegments.Add(segment);
                }
            }
            SafeRefreshCollectionView();
        }

        /// <summary>
        /// Update the available levels collection
        /// </summary>
        private void UpdateAvailableLevels()
        {
            AvailableLevels.Clear();
            if (TSData != null)
            {
                var levelNames = TSData.GetUniqueLevelNames();
                foreach (var levelName in levelNames)
                {
                    AvailableLevels.Add(levelName);
                }
            }
        }

        /// <summary>
        /// Filter tray segments by selected level
        /// </summary>
        private void FilterTraySegmentsByLevel()
        {
            SafeRefreshCollectionView();
        }

        /// <summary>
        /// Filter predicate for tray segments
        /// </summary>
        private bool FilterTraySegment(object item)
        {
            if (item is CableTraySegmentModel traySegment)
            {
                if (SelectedLevel == "All") return true;
                return string.Equals(traySegment.GetLevelName(), SelectedLevel, StringComparison.OrdinalIgnoreCase);
            }
            return false;
        }

        /// <summary>
        /// Safely refresh the collection view, handling editing states
        /// </summary>
        private void SafeRefreshCollectionView()
        {
            try
            {
                if (TraySegmentsView is IEditableCollectionView editableView)
                {
                    // Cancel any pending edits before refreshing
                    if (editableView.IsAddingNew)
                    {
                        editableView.CancelNew();
                    }
                    if (editableView.IsEditingItem)
                    {
                        editableView.CancelEdit();
                    }
                }

                TraySegmentsView.Refresh();
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error refreshing collection view: {ex.Message}", LogType.Warning);
                // If refresh fails, try to force a refresh by recreating the view
                try
                {
                    TraySegmentsView = CollectionViewSource.GetDefaultView(TraySegments);
                    TraySegmentsView.Filter = FilterTraySegment;
                }
                catch (Exception innerEx)
                {
                    _logger?.Log($"Error recreating collection view: {innerEx.Message}", LogType.Error);
                }
            }
        }

        /// <summary>
        /// Handle selected tray segment changed
        /// </summary>
        private void OnSelectedTraySegmentChangedInternal()
        {
            // Update command can execute states
            CommandManager.InvalidateRequerySuggested();

            if (SelectedTraySegment != null)
            {
                StatusMessage = $"Selected tray: {SelectedTraySegment.TrayRef} ({SelectedTraySegment.Cables.Count} cables)";
            }
            else
            {
                StatusMessage = "No tray selected";
            }
        }

        #endregion

        #region External Event Response Handling

        /// <summary>
        /// Override WakeUp to process SharedData after external events
        /// </summary>
        public override void WakeUp()
        {
            base.WakeUp();

            try
            {
                // Process SharedData based on the action performed
                if (!string.IsNullOrEmpty(SharedData.ProcessingAction))
                {
                    switch (SharedData.ProcessingAction)
                    {
                        case "AddTraySegment":
                            ProcessAddTraySegmentResponse();
                            break;
                        case "RemoveTraySegment":
                            ProcessRemoveTraySegmentResponse();
                            break;
                    }

                    // Clear processing action
                    SharedData.ProcessingAction = null;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error processing external event response: {ex.Message}";
                _logger?.Log($"Failed to process external event response: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Process the response from AddTraySegment external event
        /// </summary>
        private void ProcessAddTraySegmentResponse()
        {
            try
            {
                var selectedTray = SharedData.SelectedTrayForProcessing;
                if (selectedTray == null)
                {
                    StatusMessage = "No tray was selected";
                    _logger?.Log("ProcessAddTraySegmentResponse called but no tray was selected", LogType.Warning);
                    return;
                }

                // Validate tray element is still valid
                if (!selectedTray.IsValidObject)
                {
                    StatusMessage = "Selected tray is no longer valid in the document";
                    _logger?.Log($"Selected tray {selectedTray.Id} is no longer valid", LogType.Error);
                    return;
                }

                // Ensure TSData is initialized
                if (TSData == null)
                {
                    StatusMessage = "Data not loaded. Please click 'Load Data' first.";
                    _logger?.Log("TSData is null when trying to add tray segment", LogType.Error);
                    return;
                }

                // Check if tray already exists in the collection
                var existingSegment = TraySegments?.FirstOrDefault(ts => ts.CableTray?.Id == selectedTray.Id);
                if (existingSegment != null)
                {
                    StatusMessage = $"Tray {existingSegment.TrayRef} is already in {existingSegment.FloorPlan?.Name ?? string.Empty}";
                    SelectedTraySegment = existingSegment;
                    DialogResult result = MessageBox.Show($"Tray {existingSegment.TrayRef} already exists in {existingSegment.FloorPlan?.Name ?? string.Empty}\n\nDo you want to open the floor plan?", "Info", MessageBoxButtons.YesNo, MessageBoxIcon.Information);
                    if (result == DialogResult.Yes)
                    {
                        // Open the floor plan view

                        _uIDocument.ActiveView = existingSegment.FloorPlan;

                        _uIDocument.ShowElements(existingSegment.CableTray.Id);
                        _uIDocument.Selection.SetElementIds(new List<ElementId>() { existingSegment.CableTray.Id });
                        _uIDocument.RefreshActiveView();
                    }

                    _logger?.Log($"Tray {existingSegment.TrayRef} already exists in collection", LogType.Information);
                    return;
                }

                // Create new tray segment model
                var newSegment = CreateTraySegmentModel(selectedTray);
                if (newSegment != null)
                {
                    // Validate the new segment
                    if (string.IsNullOrEmpty(newSegment.TrayRef))
                    {
                        newSegment.TrayRef = $"Tray_{selectedTray.Id}";
                        _logger?.Log($"Generated TrayRef for tray {selectedTray.Id}", LogType.Information);
                    }

                    // Add to collections with validation
                    TraySegments.Add(newSegment);
                    TSData.AddTraySegment(newSegment);

                    // Verify the segment was added successfully
                    var addedToUI = TraySegments.Contains(newSegment);
                    var addedToData = TSData.CableTraySegments.Contains(newSegment);

                    if (!addedToUI || !addedToData)
                    {
                        StatusMessage = $"Warning: Tray segment may not have been added correctly (UI: {addedToUI}, Data: {addedToData})";
                        _logger?.Log($"Tray segment addition validation failed - UI: {addedToUI}, Data: {addedToData}", LogType.Warning);
                    }

                    // Perform AUTOMATIC CABLE DETECTION 
                    PerformAutomaticCableDetection(newSegment);

                    // Perform AUTOMATIC TAG CREATION
                    CreateAutomaticTag(newSegment);

                    // Perform AUTOMATIC PARAMETRIC CABLE CREATION
                    CreateAutomaticParametricCable(newSegment);

                    // Update available levels
                    UpdateAvailableLevels();

                    // Refresh the filtered view to show the new item
                    SafeRefreshCollectionView();

                    // Select the new segment
                    SelectedTraySegment = newSegment;

                    SelectedLevel = newSegment.GetLevelName();

                    StatusMessage = $"Added tray segment: {newSegment.TrayRef} with {newSegment.Cables.Count} cables, tag, and parametric cable created";
                    _logger?.Log($"Tray segment added successfully: {newSegment.TrayRef} with {newSegment.Cables.Count} cables, tag, and parametric cable (Total segments: {TraySegments.Count})", LogType.Information);
                }
                else
                {
                    StatusMessage = "Failed to create tray segment model";
                    _logger?.Log($"Failed to create tray segment model for tray {selectedTray.Id}", LogType.Error);
                }

                // Clear shared data
                SharedData.SelectedTrayForProcessing = null;
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error processing added tray segment: {ex.Message}";
                _logger?.Log($"Failed to process added tray segment: {ex.Message}", LogType.Error);

                // Clear shared data even on error
                SharedData.SelectedTrayForProcessing = null;
            }
        }

        /// <summary>
        /// Process the response from RemoveTraySegment external event
        /// </summary>
        private void ProcessRemoveTraySegmentResponse()
        {
            try
            {
                var segmentToRemove = SharedData.SelectedTraySegmentForProcessing;
                if (segmentToRemove != null)
                {
                    // Write TS_Cables parameter directly
                    ClearParameters(segmentToRemove);

                    // Remove from collections with validation
                    var removedFromUI = TraySegments.Remove(segmentToRemove);
                    var removedFromData = TSData?.CableTraySegments?.Remove(segmentToRemove) ?? false;

                    if (removedFromUI || removedFromData)
                    {
                        // Update available levels
                        UpdateAvailableLevels();

                        // Refresh the filtered view
                        SafeRefreshCollectionView();

                        StatusMessage = $"Removed tray segment: {segmentToRemove.TrayRef}";
                        _logger?.Log($"Tray segment removed successfully: {segmentToRemove.TrayRef} (UI: {removedFromUI}, Data: {removedFromData})", LogType.Information);
                    }
                    else
                    {
                        StatusMessage = $"Tray segment {segmentToRemove.TrayRef} was not found in collections";
                        _logger?.Log($"Tray segment {segmentToRemove.TrayRef} was not found in collections", LogType.Warning);
                    }
                }

                // Clear shared data
                SharedData.SelectedTraySegmentForProcessing = null;
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error processing removed tray segment: {ex.Message}";
                _logger?.Log($"Failed to process removed tray segment: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Create a CableTraySegmentModel from a FamilyInstance
        /// </summary>
        private CableTraySegmentModel CreateTraySegmentModel(CableTray cableTray)
        {
            try
            {
                var segment = new CableTraySegmentModel
                {
                    CableTray = cableTray,
                    TrayRef = cableTray.get_Parameter(BuiltInParameter.ALL_MODEL_MARK)?.AsString() ?? cableTray.Id.ToString(),
                    Level = _document.GetElement(cableTray.LevelId) as Level,
                    Width = GetTrayWidth(cableTray),
                    Cables = new ObservableCollection<CableModel>()
                };

                // Try to get existing cable data from TS_Cables parameter
                var tsCablesParam = cableTray.LookupParameter("TS_Cables");
                if (tsCablesParam != null && !string.IsNullOrEmpty(tsCablesParam.AsString()))
                {
                    var cableRefs = tsCablesParam.AsString().Split(',');
                    foreach (var cableRef in cableRefs)
                    {
                        var trimmedRef = cableRef.Trim();
                        var cable = TSData?.AllCables?.Values?.FirstOrDefault(c => c.CableRef == trimmedRef);
                        if (cable != null)
                        {
                            segment.Cables.Add(cable);
                            cable.AssociatedTraySegment = segment;
                        }
                    }
                }

                // Calculate capacity and weight (NumberOfCables is computed property)
                segment.Capacity = _trayAnalysisService?.CalculateCapacityUtilization(segment) ?? 0;
                segment.Weight = segment.Cables.Sum(c => c.Weight);

                return segment;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to create tray segment model: {ex.Message}", LogType.Error);
                return null;
            }
        }

        /// <summary>
        /// Perform automatic cable detection for a newly added tray segment
        /// This implements the documented requirement for automatic cable matching
        /// </summary>
        private void PerformAutomaticCableDetection(CableTraySegmentModel traySegment)
        {
            try
            {
                if (traySegment?.CableTray == null || TSData?.AllCables == null)
                {
                    _logger?.Log("Cannot perform cable detection - missing tray or cable data", LogType.Warning);
                    return;
                }

                _logger?.Log($"Starting automatic cable detection for tray {traySegment.TrayRef}", LogType.Information);

                // Configure detection options for automatic operation
                var options = new Models.CableDetectionOptions
                {
                    UseGeometricIntersection = true,
                    UseCircuitPathAnalysis = true,
                    UseProximityAnalysis = true,
                    MinimumConfidenceScore = 0.4,
                    IntersectionTolerance = 0.1,
                    ProximityThreshold = 2.0, // 2 feet? proximity threshold
                    MaximumCablesPerTray = 50
                };

                // Get electrical circuits for circuit path analysis
                var circuits = _cableDetectionService.FindAllElectricalCircuits(); //PerformCablesInTrayDetection

                // Perform cable in tray detection
                var detectionResults = _cableDetectionService.PerformCablesInTrayDetection(
                    traySegment.CableTray,TSData.AllCables,options);

                //// Perform multi-criteria cable detection
                //var detectionResults = _cableDetectionService.PerformMultiCriteriaCableDetection(
                //    traySegment.CableTray,
                //    TSData.AllCables,
                //    circuits,
                //    options);

                // Clear existing cables and add detected ones
                traySegment.Cables.Clear();
                foreach (var result in detectionResults)
                {
                    traySegment.Cables.Add(result.Cable);
                    result.Cable.AssociatedTraySegment = traySegment;
                    _logger?.Log($"Detected cable {result.Cable.CableRef} with confidence {result.ConfidenceScore:F2} ({result.Reason})", LogType.Information);
                }

                // Create comma-separated cable references string
                var cableRefs = string.Join(",", detectionResults.Select(r => r.Cable.CableRef));

                // Write TS_Cables parameter directly (avoiding external event to prevent recursion)
                WriteCablesParameterDirectly(traySegment, cableRefs);

                _logger?.Log($"Automatic cable detection completed for tray {traySegment.TrayRef}: {traySegment.Cables.Count} cables detected", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to perform automatic cable detection: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Create automatic tag for the newly added tray segment in the active Floor Plan
        /// This implements the documented requirement for automatic tag creation
        /// </summary>
        private void CreateAutomaticTag(CableTraySegmentModel traySegment)
        {
            try
            {
                if (traySegment?.CableTray == null)
                {
                    _logger?.Log("Cannot create automatic tag - missing tray segment or cable tray", LogType.Warning);
                    return;
                }

                // Verify we're in a Floor Plan view (as required by documentation)
                var activeView = _document.ActiveView;
                if (!(activeView is ViewPlan))
                {
                    _logger?.Log($"Cannot create tag - active view is not a Floor Plan (current: {activeView?.ViewType})", LogType.Warning);
                    return;
                }

                _logger?.Log($"Creating automatic tag for tray {traySegment.TrayRef} in Floor Plan view {activeView.Name}", LogType.Information);

                // Validate tag creation requirements
                var validationIssues = _taggingService.ValidateTagCreationRequirements(traySegment.CableTray, activeView);
                if (validationIssues.Count > 0)
                {
                    _logger?.Log($"Tag creation validation failed: {string.Join("; ", validationIssues)}", LogType.Warning);
                    return;
                }

                // Create the tag using the tagging service
                var tag = _taggingService.CreateTrayTag(traySegment.CableTray, traySegment);

                if (tag != null)
                {
                    // Update the tray segment model
                    traySegment.Tag = tag;

                    // Write the tag reference to the tray parameter
                    WriteTagParameterDirectly(traySegment, tag.Id);

                    _logger?.Log($"Successfully created automatic tag for tray {traySegment.TrayRef} showing {traySegment.Cables.Count} cables", LogType.Information);
                }
                else
                {
                    _logger?.Log($"Failed to create automatic tag for tray {traySegment.TrayRef}", LogType.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error creating automatic tag: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Create automatic parametric cable for the newly added tray segment
        /// This implements the documented requirement for automatic BecaParametricCable creation
        /// </summary>
        private void CreateAutomaticParametricCable(CableTraySegmentModel traySegment)
        {
            try
            {
                if (traySegment?.CableTray == null)
                {
                    _logger?.Log("Cannot create automatic parametric cable - missing tray segment or cable tray", LogType.Warning);
                    return;
                }

                _logger?.Log($"Creating automatic parametric cable for tray {traySegment.TrayRef}", LogType.Information);

                // Create the parametric cable using the service
                var parametricCable = _parametricCableService.CreateParametricCable(traySegment.CableTray, traySegment);

                if (parametricCable != null)
                {
                    // Update the tray segment model
                    traySegment.BecaParametricCable = parametricCable;

                    // Write the parametric cable reference to the tray parameter
                    WriteParametricCableParameterDirectly(traySegment, parametricCable.Id);

                    _logger?.Log($"Successfully created automatic parametric cable for tray {traySegment.TrayRef}", LogType.Information);
                }
                else
                {
                    _logger?.Log($"Failed to create automatic parametric cable for tray {traySegment.TrayRef}", LogType.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error creating automatic parametric cable: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Write TS_ParametricCable parameter directly without using external event
        /// This prevents recursion issues when called from within WakeUp()
        /// </summary>
        private void WriteParametricCableParameterDirectly(CableTraySegmentModel traySegment, ElementId parametricCableId)
        {
            try
            {
                if (traySegment?.CableTray == null || parametricCableId == null) return;

                // We need to use a transaction since we're modifying Revit elements
                using (var transaction = new Autodesk.Revit.DB.Transaction(_document, "Write TS_ParametricCable Parameter"))
                {
                    transaction.Start();

                    try
                    {
                        // Write the TS_ParametricCable parameter directly using the service
                        var success = _trayAnalysisService.WriteTSParametricCableParameter(traySegment.CableTray, parametricCableId); 

                        if (success)
                        {
                            transaction.Commit();
                            _logger?.Log($"Directly wrote TS_ParametricCable parameter for tray {traySegment.TrayRef}: {parametricCableId}", LogType.Information);
                        }
                        else
                        {
                            transaction.RollBack();
                            _logger?.Log($"Failed to write TS_ParametricCable parameter for tray {traySegment.TrayRef}", LogType.Error);
                        }
                    }
                    catch (Exception innerEx)
                    {
                        transaction.RollBack();
                        _logger?.Log($"Transaction failed while writing TS_ParametricCable parameter: {innerEx.Message}", LogType.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to write TS_ParametricCable parameter directly: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Write TS_SectionView parameter directly without using external event
        /// This prevents recursion issues when called from within WakeUp()
        /// </summary>
        private void WriteSectionViewParameterDirectly(CableTraySegmentModel traySegment, ElementId parametricCableId)
        {
            try
            {
                if (traySegment?.CableTray == null || parametricCableId == null) return;

                // We need to use a transaction since we're modifying Revit elements
                using (var transaction = new Autodesk.Revit.DB.Transaction(_document, "Write TS_SectionView Parameter"))
                {
                    transaction.Start();

                    try
                    {
                        // Write the TS_SectionView parameter directly using the service
                        var success = _trayAnalysisService.WriteTSSectionViewParameter(traySegment.CableTray, parametricCableId);

                        if (success)
                        {
                            transaction.Commit();
                            _logger?.Log($"Directly wrote TS_SectionView parameter for tray {traySegment.TrayRef}: {parametricCableId}", LogType.Information);
                        }
                        else
                        {
                            transaction.RollBack();
                            _logger?.Log($"Failed to write TS_SectionView parameter for tray {traySegment.TrayRef}", LogType.Error);
                        }
                    }
                    catch (Exception innerEx)
                    {
                        transaction.RollBack();
                        _logger?.Log($"Transaction failed while writing TS_SectionView parameter: {innerEx.Message}", LogType.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to write TS_SectionView parameter directly: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Write TS_Tag parameter directly without using external event
        /// This prevents recursion issues when called from within WakeUp()
        /// </summary>
        private void WriteTagParameterDirectly(CableTraySegmentModel traySegment, ElementId tagId)
        {
            try
            {
                if (traySegment?.CableTray == null || tagId == null) return;

                // We need to use a transaction since we're modifying Revit elements
                using (var transaction = new Autodesk.Revit.DB.Transaction(_document, "Write TS_Tag Parameter"))
                {
                    transaction.Start();

                    try
                    {
                        // Write the TS_Tag parameter directly using the service
                        var success = _trayAnalysisService.WriteTSTagParameter(traySegment.CableTray, tagId);

                        if (success)
                        {
                            transaction.Commit();
                            _logger?.Log($"Directly wrote TS_Tag parameter for tray {traySegment.TrayRef}: {tagId}", LogType.Information);
                        }
                        else
                        {
                            transaction.RollBack();
                            _logger?.Log($"Failed to write TS_Tag parameter for tray {traySegment.TrayRef}", LogType.Error);
                        }
                    }
                    catch (Exception innerEx)
                    {
                        transaction.RollBack();
                        _logger?.Log($"Transaction failed while writing TS_Tag parameter: {innerEx.Message}", LogType.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to write TS_Tag parameter directly: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Write TS_Cables parameter directly without using external event
        /// This prevents recursion issues when called from within WakeUp()
        /// </summary>
        private void WriteCablesParameterDirectly(CableTraySegmentModel traySegment, string cableRefs)
        {
            try
            {
                if (traySegment?.CableTray == null) return;

                // We need to use a transaction since we're modifying Revit elements
                // This is safe to call from WakeUp() as we're already in the Revit API context
                using (var transaction = new Autodesk.Revit.DB.Transaction(_document, "Write TS_Cables Parameter"))
                {
                    transaction.Start();

                    try
                    {
                        // Write the TS_Cables parameter directly using the service
                        var success = _trayAnalysisService.WriteTSCablesParameter(traySegment.CableTray, cableRefs);

                        if (success)
                        {
                            transaction.Commit();
                            _logger?.Log($"Directly wrote TS_Cables parameter for tray {traySegment.TrayRef}: '{cableRefs}'", LogType.Information);
                        }
                        else
                        {
                            transaction.RollBack();
                            _logger?.Log($"Failed to write TS_Cables parameter for tray {traySegment.TrayRef}", LogType.Error);
                        }
                    }
                    catch (Exception innerEx)
                    {
                        transaction.RollBack();
                        _logger?.Log($"Transaction failed while writing TS_Cables parameter: {innerEx.Message}", LogType.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to write TS_Cables parameter directly: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Clear all segment's related elements (tag, parametric family, section)
        /// and tray parameters
        /// </summary>
        private void ClearParameters(CableTraySegmentModel traySegment)
        {
            try
            {
                if (traySegment?.CableTray == null) return;

                // We need to use a transaction since we're modifying Revit elements
                // This is safe to call from WakeUp() as we're already in the Revit API context
                using (var transaction = new Autodesk.Revit.DB.Transaction(_document, "Write TS_Cables Parameter"))
                {
                    transaction.Start();

                    try
                    {
                        // Delete Tag
                        if (traySegment.Tag != null)
                        {
                            _document.Delete(traySegment.Tag.Id);
                            // Set tag Comments parameter to empty string, removing cable info
                            var textParam = traySegment.CableTray.get_Parameter(BuiltInParameter.ALL_MODEL_INSTANCE_COMMENTS);
                            if (textParam != null && !textParam.IsReadOnly)
                            {
                                textParam.Set(string.Empty);
                            }
                        }
                            
                        // Delete Parameteric Cable
                        if (traySegment.BecaParametricCable != null)
                            _document.Delete(traySegment.BecaParametricCable.Id);
                        // Delete Section View
                        if (traySegment.SectionView != null)
                            _document.Delete(traySegment.SectionView.Id);

                        // Write the TS_Cables parameter directly using the service
                        traySegment.CableTray.LookupParameter(TSParameterNames.TS_Cables)?.Set(string.Empty); 
                        traySegment.CableTray.LookupParameter(TSParameterNames.TS_ParametricCable)?.Set(string.Empty);
                        traySegment.CableTray.LookupParameter(TSParameterNames.TS_SectionView)?.Set(string.Empty);
                        traySegment.CableTray.LookupParameter(TSParameterNames.TS_Tag)?.Set(string.Empty);

                        transaction.Commit();
                        _logger?.Log($"Parameters cleared for tay segment: {traySegment.CableTray.Id}", LogType.Information);
                        
                    }
                    catch (Exception innerEx)
                    {
                        transaction.RollBack();
                        _logger?.Log($"Transaction failed while writing TS_Cables parameter: {innerEx.Message}", LogType.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to write TS_Cables parameter directly: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Write initial TS_Cables parameter to ensure tray segment persists across loads
        /// </summary>
        private void WriteInitialTSCablesParameter(CableTraySegmentModel traySegment)
        {
            try
            {
                if (traySegment?.CableTray == null) return;

                // Get current cable references or use empty string for new segments
                var cableRefs = traySegment.GetCablesParameterValue();

                // Store the tray segment and cable data for parameter writing via external event
                SharedData.SelectedTraySegmentForProcessing = traySegment;
                SharedData.ProcessingAction = "WriteTSCablesParameter";

                // Request parameter writing through external event
                MakeRequest(RequestId.WriteTSCablesParameter);

                _logger?.Log($"Requested TS_Cables parameter write for tray {traySegment.TrayRef}", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to write initial TS_Cables parameter: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Get tray width from family instance
        /// </summary>
        private double GetTrayWidth(CableTray cableTray)
        {
            try
            {
                var widthParam = cableTray.get_Parameter(BuiltInParameter.CURVE_ELEM_LENGTH) ??
                                cableTray.get_Parameter(BuiltInParameter.GENERIC_WIDTH) ??
                                cableTray.LookupParameter("Width");

                return widthParam?.AsDouble() ?? 0.0;
            }
            catch
            {
                return 0.0;
            }
        }

        #endregion
    }
}
