﻿using System;
using System.Windows.Controls;
using System.Windows.Navigation;
using Frame = System.Windows.Controls.Frame;

namespace MEP.TraylorSwift.Services
{
    /// <summary>
    /// Navigation service for managing page navigation within the main window
    /// </summary>
    public class NavigationService
    {
        #region Fields

        private Frame _navigationFrame;

        #endregion

        #region Events

        /// <summary>
        /// Event raised when navigation occurs
        /// </summary>
        public event EventHandler<PageNavigationEventArgs> Navigated;

        #endregion

        #region Properties

        /// <summary>
        /// Current page being displayed
        /// </summary>
        public Page CurrentPage => _navigationFrame?.Content as Page;

        /// <summary>
        /// Whether navigation can go back
        /// </summary>
        public bool CanGoBack => _navigationFrame?.CanGoBack ?? false;

        #endregion

        #region Methods

        /// <summary>
        /// Initialize the navigation service with a Frame
        /// </summary>
        /// <param name="navigationFrame">The Frame to use for navigation</param>
        public void Initialize(Frame navigationFrame)
        {
            _navigationFrame = navigationFrame ?? throw new ArgumentNullException(nameof(navigationFrame));
            _navigationFrame.Navigated += OnFrameNavigated;
        }

        /// <summary>
        /// Navigate to a page
        /// </summary>
        /// <param name="page">The page to navigate to</param>
        public void NavigateTo(Page page)
        {
            if (_navigationFrame == null)
                throw new InvalidOperationException("NavigationService not initialized. Call Initialize() first.");

            if (page == null)
                throw new ArgumentNullException(nameof(page));

            _navigationFrame.Navigate(page);
        }

        /// <summary>
        /// Navigate to a page by type
        /// </summary>
        /// <typeparam name="T">The page type</typeparam>
        /// <param name="dataContext">Optional data context for the page</param>
        public void NavigateTo<T>(object dataContext = null) where T : Page, new()
        {
            var page = new T();
            if (dataContext != null)
            {
                page.DataContext = dataContext;
            }
            NavigateTo(page);
        }

        /// <summary>
        /// Go back to the previous page
        /// </summary>
        public void GoBack()
        {
            if (_navigationFrame?.CanGoBack == true)
            {
                _navigationFrame.GoBack();
            }
        }

        /// <summary>
        /// Clear navigation history
        /// </summary>
        public void ClearHistory()
        {
            if (_navigationFrame != null)
            {
                while (_navigationFrame.CanGoBack)
                {
                    _navigationFrame.RemoveBackEntry();
                }
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handle frame navigation events
        /// </summary>
        private void OnFrameNavigated(object sender, System.Windows.Navigation.NavigationEventArgs e)
        {
            var customArgs = new PageNavigationEventArgs
            {
                Page = e.Content as Page,
                DataContext = (e.Content as Page)?.DataContext,
                PageName = e.Content?.GetType().Name
            };

            Navigated?.Invoke(this, customArgs);
        }

        #endregion
    }

    /// <summary>
    /// Page Navigation event arguments
    /// </summary>
    public class PageNavigationEventArgs : EventArgs
    {
        public Page Page { get; set; }
        public object DataContext { get; set; }
        public string PageName { get; set; }
    }
}
