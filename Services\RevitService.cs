﻿using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using MEP.TraylorSwift.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.TraylorSwift.Services
{
    public class RevitService : IRevitService
    {
        #region Fields

        private readonly UIDocument _uiDocument;
        private readonly BecaActivityLoggerData _logger;

        #endregion

        #region Properties

        public Document Document => _uiDocument?.Document;
        public UIDocument UIDocument => _uiDocument;
        public UIApplication UIApplication => _uiDocument?.Application;

        #endregion

        #region Constructor

        public RevitService(UIDocument uiDocument, BecaActivityLoggerData logger)
        {
            _uiDocument = uiDocument ?? throw new ArgumentNullException(nameof(uiDocument));
            _logger = logger;
        }

        #endregion

        #region Transaction Management

        public bool ExecuteInTransaction(string transactionName, Func<bool> operation)
        {
            try
            {
                using (var trans = new Transaction(Document, transactionName))
                {
                    trans.Start();

                    var result = operation();

                    if (result)
                    {
                        trans.Commit();
                        _logger?.Log($"Transaction '{transactionName}' committed successfully", LogType.Information);
                    }
                    else
                    {
                        trans.RollBack();
                        _logger?.Log($"Transaction '{transactionName}' rolled back", LogType.Warning);
                    }

                    return result;
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Transaction '{transactionName}' failed: {ex.Message}", LogType.Error);
                return false;
            }
        }

        #endregion
    }
}
