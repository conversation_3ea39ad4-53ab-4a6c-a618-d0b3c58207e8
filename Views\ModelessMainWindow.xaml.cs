﻿using Autodesk.Revit.DB;
using MEP.TraylorSwift.ViewModels;
using System;
using System.ComponentModel;
using System.Windows;

namespace MEP.TraylorSwift.Views
{
    /// <summary>
    /// Interaction logic for ModelessMainWindow.xaml
    /// </summary>
    public partial class ModelessMainWindow : Window
    {
        #region Fields

        private TrayTaggerViewModel _viewModel;

        #endregion

        #region Properties

        /// <summary>
        /// The ViewModel for this window
        /// </summary>
        public TrayTaggerViewModel ViewModel
        {
            get => _viewModel;
            set
            {
                if (_viewModel != value)
                {
                    _viewModel = value;
                    DataContext = _viewModel;
                }
            }
        }

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the ModelessMainWindow
        /// </summary>
        public ModelessMainWindow()
        {
            InitializeComponent();

            // Set window properties for modeless operation
            ShowInTaskbar = false;
            WindowStartupLocation = WindowStartupLocation.Manual;

            // Handle window closing to hide instead of close
            Closing += OnWindowClosing;
        }

        /// <summary>
        /// Initialize the ModelessMainWindow with a ViewModel
        /// </summary>
        /// <param name="viewModel">The ViewModel to use</param>
        public ModelessMainWindow(TrayTaggerViewModel viewModel) : this()
        {
            ViewModel = viewModel;

            // Initialize navigation after the window is loaded
            Loaded += (s, e) => InitializeNavigation();
        }

        /// <summary>
        /// Initialize navigation service and navigate to default page
        /// </summary>
        private void InitializeNavigation()
        {
            try
            {
                if (ViewModel != null && MainContentFrame != null)
                {
                    System.Diagnostics.Debug.WriteLine("Initializing navigation...");
                    ViewModel.InitializeNavigation(MainContentFrame);
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"ViewModel is null: {ViewModel == null}, MainContentFrame is null: {MainContentFrame == null}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing navigation: {ex.Message}");
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handle window closing event to hide instead of close
        /// </summary>
        private void OnWindowClosing(object sender, CancelEventArgs e)
        {
            //try
            //{
            //    // Hide the window instead of closing it for modeless operation
            //    e.Cancel = true;
            //    Hide();
            //}
            //catch (Exception ex)
            //{
            //    // Log error but don't prevent closing
            //    System.Diagnostics.Debug.WriteLine($"Error hiding window: {ex.Message}");
            //}
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Show the window and bring it to front
        /// </summary>
        public new void Show()
        {
            try
            {
                base.Show();

                // Bring window to front
                if (WindowState == WindowState.Minimized)
                {
                    WindowState = WindowState.Normal;
                }

                Activate();
                Topmost = true;
                Topmost = false;
                Focus();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error showing window: {ex.Message}");
            }
        }

        /// <summary>
        /// Toggle window visibility
        /// </summary>
        public void ToggleVisibility()
        {
            try
            {
                if (IsVisible)
                {
                    Hide();
                }
                else
                {
                    Show();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error toggling window visibility: {ex.Message}");
            }
        }

        /// <summary>
        /// Close the window permanently
        /// </summary>
        public void CloseWindow()
        {
            try
            {
                // Remove the closing event handler to allow actual closing
                Closing -= OnWindowClosing;
                Close();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error closing window: {ex.Message}");
            }
        }

        #endregion
    }
}
