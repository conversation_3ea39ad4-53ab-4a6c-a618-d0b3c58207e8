using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.Linq;

namespace MEP.TraylorSwift.Models
{
    /// <summary>
    /// Main data container for Traylor Swift application
    /// Stores all cable tray segments, cables, and project information
    /// </summary>
    public class TS_Data
    {
        #region Properties

        /// <summary>
        /// List of all cable tray segments in the project
        /// </summary>
        public List<CableTraySegmentModel> CableTraySegments { get; set; }

        /// <summary>
        /// Project information from Revit
        /// </summary>
        public ProjectInfo ProjectInfo { get; set; }

        /// <summary>
        /// Dictionary of all cables indexed by cable reference
        /// </summary>
        public Dictionary<string, CableModel> AllCables { get; set; }

        /// <summary>
        /// Dictionary of all electrical equipment indexed by element ID
        /// </summary>
        public Dictionary<ElementId, Element> ElectricalEquipment { get; set; }

        /// <summary>
        /// Dictionary of all levels indexed by element ID
        /// </summary>
        public Dictionary<ElementId, Level> Levels { get; set; }

        /// <summary>
        /// Spatial index for efficient geometric queries (will be implemented later)
        /// </summary>
        public object SpatialIndex { get; set; }

        /// <summary>
        /// Timestamp when data was last loaded or refreshed
        /// </summary>
        public DateTime LastUpdated { get; set; }

        /// <summary>
        /// Document from which this data was loaded
        /// </summary>
        public Document Document { get; set; }

        /// <summary>
        /// Indicates if the data has been modified since last save
        /// </summary>
        public bool IsModified { get; set; }

        /// <summary>
        /// Application settings and preferences
        /// </summary>
        public Dictionary<string, object> Settings { get; set; }

        /// <summary>
        /// Statistics about the loaded data
        /// </summary>
        public TS_DataStatistics Statistics { get; set; }

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize a new TS_Data instance
        /// </summary>
        public TS_Data()
        {
            CableTraySegments = new List<CableTraySegmentModel>();
            AllCables = new Dictionary<string, CableModel>();
            ElectricalEquipment = new Dictionary<ElementId, Element>();
            Levels = new Dictionary<ElementId, Level>();
            Settings = new Dictionary<string, object>();
            Statistics = new TS_DataStatistics();
            LastUpdated = DateTime.Now;
        }

        /// <summary>
        /// Initialize TS_Data with a Revit document
        /// </summary>
        /// <param name="document">Revit document</param>
        public TS_Data(Document document) : this()
        {
            Document = document;
            ProjectInfo = document?.ProjectInformation;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Add a cable tray segment to the data
        /// </summary>
        /// <param name="traySegment">Tray segment to add</param>
        public void AddTraySegment(CableTraySegmentModel traySegment)
        {
            if (traySegment == null) return;

            if (!CableTraySegments.Contains(traySegment))
            {
                CableTraySegments.Add(traySegment);
                IsModified = true;
                UpdateStatistics();

                // Add cables from this segment to the global cable dictionary
                foreach (var cable in traySegment.Cables)
                {
                    if (!string.IsNullOrEmpty(cable.CableRef) && !AllCables.ContainsKey(cable.CableRef))
                    {
                        AllCables[cable.CableRef] = cable;
                    }
                }
            }
        }

        /// <summary>
        /// Remove a cable tray segment from the data
        /// </summary>
        /// <param name="traySegment">Tray segment to remove</param>
        /// <returns>True if segment was removed</returns>
        public bool RemoveTraySegment(CableTraySegmentModel traySegment)
        {
            if (traySegment == null) return false;

            bool removed = CableTraySegments.Remove(traySegment);
            if (removed)
            {
                IsModified = true;
                UpdateStatistics();

                // Remove cables that are no longer referenced
                CleanupOrphanedCables();
            }
            return removed;
        }

        /// <summary>
        /// Get tray segment by reference
        /// </summary>
        /// <param name="trayRef">Tray reference to find</param>
        /// <returns>Tray segment or null if not found</returns>
        public CableTraySegmentModel GetTraySegmentByRef(string trayRef)
        {
            if (string.IsNullOrEmpty(trayRef)) return null;

            return CableTraySegments.FirstOrDefault(t => 
                string.Equals(t.TrayRef, trayRef, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Get tray segment by element ID
        /// </summary>
        /// <param name="elementId">Element ID to find</param>
        /// <returns>Tray segment or null if not found</returns>
        public CableTraySegmentModel GetTraySegmentByElementId(ElementId elementId)
        {
            if (elementId == null) return null;

            return CableTraySegments.FirstOrDefault(t => t.CableTray?.Id == elementId);
        }

        /// <summary>
        /// Get all tray segments on a specific level
        /// </summary>
        /// <param name="level">Level to filter by</param>
        /// <returns>List of tray segments on the specified level</returns>
        public List<CableTraySegmentModel> GetTraySegmentsByLevel(Level level)
        {
            if (level == null) return new List<CableTraySegmentModel>();

            return CableTraySegments.Where(t => t.Level?.Id == level.Id).ToList();
        }

        /// <summary>
        /// Get all tray segments on a specific level by name
        /// </summary>
        /// <param name="levelName">Level name to filter by</param>
        /// <returns>List of tray segments on the specified level</returns>
        public List<CableTraySegmentModel> GetTraySegmentsByLevelName(string levelName)
        {
            if (string.IsNullOrEmpty(levelName)) return new List<CableTraySegmentModel>();

            return CableTraySegments.Where(t => 
                string.Equals(t.GetLevelName(), levelName, StringComparison.OrdinalIgnoreCase)).ToList();
        }

        /// <summary>
        /// Get cable by reference
        /// </summary>
        /// <param name="cableRef">Cable reference to find</param>
        /// <returns>Cable model or null if not found</returns>
        public CableModel GetCableByRef(string cableRef)
        {
            if (string.IsNullOrEmpty(cableRef)) return null;

            AllCables.TryGetValue(cableRef, out var cable);
            return cable;
        }

        /// <summary>
        /// Add or update a cable in the global dictionary
        /// </summary>
        /// <param name="cable">Cable to add or update</param>
        public void AddOrUpdateCable(CableModel cable)
        {
            if (cable == null || string.IsNullOrEmpty(cable.CableRef)) return;

            AllCables[cable.CableRef] = cable;
            IsModified = true;
            UpdateStatistics();
        }

        /// <summary>
        /// Get all unique levels from tray segments
        /// </summary>
        /// <returns>List of unique levels</returns>
        public List<Level> GetUniqueLevels()
        {
            return CableTraySegments
                .Where(t => t.Level != null)
                .Select(t => t.Level)
                .Distinct()
                .OrderBy(l => l.Elevation)
                .ToList();
        }

        /// <summary>
        /// Get all unique level names from tray segments
        /// </summary>
        /// <returns>List of unique level names</returns>
        public List<string> GetUniqueLevelNames()
        {
            var levelNames = CableTraySegments
                .Select(t => t.GetLevelName())
                .Where(name => !string.IsNullOrEmpty(name))
                .Distinct()
                .OrderBy(name => name)
                .ToList();

            // Add "All" option at the beginning
            levelNames.Insert(0, "All");
            return levelNames;
        }

        /// <summary>
        /// Clear all data
        /// </summary>
        public void Clear()
        {
            CableTraySegments.Clear();
            AllCables.Clear();
            ElectricalEquipment.Clear();
            Levels.Clear();
            SpatialIndex = null;
            IsModified = true;
            UpdateStatistics();
        }

        /// <summary>
        /// Refresh data from the Revit document
        /// </summary>
        public void RefreshFromDocument()
        {
            if (Document == null) return;

            LastUpdated = DateTime.Now;
            IsModified = false;
            UpdateStatistics();
        }

        /// <summary>
        /// Validate data integrity
        /// </summary>
        /// <returns>List of validation issues</returns>
        public List<string> ValidateData()
        {
            var issues = new List<string>();

            // Check for duplicate tray references
            var duplicateTrayRefs = CableTraySegments
                .GroupBy(t => t.TrayRef)
                .Where(g => g.Count() > 1)
                .Select(g => g.Key);

            foreach (var dupRef in duplicateTrayRefs)
            {
                issues.Add($"Duplicate tray reference: {dupRef}");
            }

            // Check for duplicate cable references
            var duplicateCableRefs = AllCables
                .GroupBy(kvp => kvp.Key)
                .Where(g => g.Count() > 1)
                .Select(g => g.Key);

            foreach (var dupRef in duplicateCableRefs)
            {
                issues.Add($"Duplicate cable reference: {dupRef}");
            }

            // Check for orphaned cables
            var orphanedCables = AllCables.Values
                .Where(c => c.AssociatedTraySegment == null)
                .Select(c => c.CableRef);

            foreach (var orphanRef in orphanedCables)
            {
                issues.Add($"Orphaned cable: {orphanRef}");
            }

            // Check for invalid Revit elements
            var invalidTrays = CableTraySegments
                .Where(t => t.CableTray == null || !t.CableTray.IsValidObject)
                .Select(t => t.TrayRef);

            foreach (var invalidRef in invalidTrays)
            {
                issues.Add($"Invalid tray element: {invalidRef}");
            }

            return issues;
        }

        /// <summary>
        /// Get setting value
        /// </summary>
        /// <typeparam name="T">Type of setting value</typeparam>
        /// <param name="key">Setting key</param>
        /// <param name="defaultValue">Default value if setting not found</param>
        /// <returns>Setting value or default</returns>
        public T GetSetting<T>(string key, T defaultValue = default(T))
        {
            if (string.IsNullOrEmpty(key) || !Settings.ContainsKey(key))
                return defaultValue;

            try
            {
                return (T)Settings[key];
            }
            catch
            {
                return defaultValue;
            }
        }

        /// <summary>
        /// Set setting value
        /// </summary>
        /// <param name="key">Setting key</param>
        /// <param name="value">Setting value</param>
        public void SetSetting(string key, object value)
        {
            if (string.IsNullOrEmpty(key)) return;

            Settings[key] = value;
            IsModified = true;
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Remove cables that are no longer referenced by any tray segment
        /// </summary>
        private void CleanupOrphanedCables()
        {
            var referencedCables = new HashSet<string>();
            
            foreach (var traySegment in CableTraySegments)
            {
                foreach (var cable in traySegment.Cables)
                {
                    if (!string.IsNullOrEmpty(cable.CableRef))
                    {
                        referencedCables.Add(cable.CableRef);
                    }
                }
            }

            var orphanedKeys = AllCables.Keys.Where(key => !referencedCables.Contains(key)).ToList();
            foreach (var key in orphanedKeys)
            {
                AllCables.Remove(key);
            }
        }

        /// <summary>
        /// Update statistics about the data
        /// </summary>
        private void UpdateStatistics()
        {
            Statistics.TotalTraySegments = CableTraySegments.Count;
            Statistics.TotalCables = AllCables.Count;
            Statistics.TotalLevels = GetUniqueLevels().Count;
            Statistics.TotalElectricalEquipment = ElectricalEquipment.Count;
            Statistics.LastUpdated = DateTime.Now;

            // Calculate capacity statistics
            if (CableTraySegments.Count > 0)
            {
                Statistics.AverageCapacity = CableTraySegments.Average(t => t.Capacity);
                Statistics.MaxCapacity = CableTraySegments.Max(t => t.Capacity);
                Statistics.MinCapacity = CableTraySegments.Min(t => t.Capacity);
            }
        }

        #endregion
    }

    /// <summary>
    /// Statistics about the TS_Data
    /// </summary>
    public class TS_DataStatistics
    {
        public int TotalTraySegments { get; set; }
        public int TotalCables { get; set; }
        public int TotalLevels { get; set; }
        public int TotalElectricalEquipment { get; set; }
        public double AverageCapacity { get; set; }
        public double MaxCapacity { get; set; }
        public double MinCapacity { get; set; }
        public DateTime LastUpdated { get; set; }

        public TS_DataStatistics()
        {
            LastUpdated = DateTime.Now;
        }

        public override string ToString()
        {
            return $"Trays: {TotalTraySegments}, Cables: {TotalCables}, Levels: {TotalLevels}, Avg Capacity: {AverageCapacity:F1}%";
        }
    }
}
