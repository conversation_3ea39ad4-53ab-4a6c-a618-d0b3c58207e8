using BecaActivityLogger.CoreLogic.Data;
using CommunityToolkit.Mvvm.Input;
using MEP.TraylorSwift.Handlers;
using MEP.TraylorSwift.Models;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows.Data;
using System.Windows.Input;

namespace MEP.TraylorSwift.ViewModels
{
    /// <summary>
    /// ViewModel for Cable Viewer window
    /// </summary>
    public partial class CableViewerViewModel : BaseViewModel
    {
        #region Fields

        private readonly TS_Data _tsData;
        private ObservableCollection<CableModel> _cables;
        private ICollectionView _cablesView;
        private CableModel _selectedCable;
        private string _searchText;

        #endregion

        #region Properties

        /// <summary>
        /// Collection of all cables
        /// </summary>
        public ObservableCollection<CableModel> Cables
        {
            get => _cables;
            set => SetProperty(ref _cables, value);
        }

        /// <summary>
        /// Filtered view of cables
        /// </summary>
        public ICollectionView CablesView
        {
            get => _cablesView;
            set => SetProperty(ref _cablesView, value);
        }

        /// <summary>
        /// Selected cable
        /// </summary>
        public CableModel SelectedCable
        {
            get => _selectedCable;
            set => SetProperty(ref _selectedCable, value);
        }

        /// <summary>
        /// Search text for filtering cables
        /// </summary>
        public string SearchText
        {
            get => _searchText;
            set
            {
                if (SetProperty(ref _searchText, value))
                {
                    CablesView?.Refresh();
                }
            }
        }

        #endregion

        #region Commands

        /// <summary>
        /// Command to show cable in Revit 3D view
        /// </summary>
        public ICommand ShowInRevitCommand { get; }

        /// <summary>
        /// Command to clear search text
        /// </summary>
        public ICommand ClearSearchCommand { get; }

        /// <summary>
        /// Command to close the window
        /// </summary>
        public ICommand CloseCommand { get; }

        /// <summary>
        /// Command to navigate back to tray segments page
        /// </summary>
        public ICommand NavigateBackCommand { get; }

        #endregion

        #region Events

        /// <summary>
        /// Event raised when the window should be closed
        /// </summary>
        public event EventHandler CloseRequested;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the CableViewerViewModel
        /// </summary>
        public CableViewerViewModel(
            TS_Data tsData,
            IServiceProvider serviceProvider) : base(serviceProvider)
        {
            _tsData = tsData ?? throw new ArgumentNullException(nameof(tsData));

            // Initialize commands
            ShowInRevitCommand = new RelayCommand<CableModel>(ShowInRevit, cable => cable != null);
            ClearSearchCommand = new RelayCommand(ClearSearch);
            CloseCommand = new RelayCommand(Close);
            NavigateBackCommand = new RelayCommand(NavigateBack);

            // Initialize data
            LoadCables();
        }

        #endregion

        #region Methods

        /// <summary>
        /// Load cables from TS_Data
        /// </summary>
        private void LoadCables()
        {
            try
            {
                Cables = new ObservableCollection<CableModel>();

                if (_tsData?.AllCables != null)
                {
                    foreach (var cable in _tsData.AllCables.Values)
                    {
                        Cables.Add(cable);
                    }
                }

                // Create filtered view
                CablesView = CollectionViewSource.GetDefaultView(Cables);
                CablesView.Filter = FilterCable;

                StatusMessage = $"Loaded {Cables.Count} cables.";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error loading cables: {ex.Message}";
                _logger?.Log($"Failed to load cables: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Filter cables based on search text
        /// </summary>
        private bool FilterCable(object obj)
        {
            if (obj is CableModel cable)
            {
                if (string.IsNullOrWhiteSpace(SearchText))
                    return true;

                var searchLower = SearchText.ToLower();
                return cable.CableRef?.ToLower().Contains(searchLower) == true ||
                       cable.Name?.ToLower().Contains(searchLower) == true ||
                       cable.GetToEquipmentName()?.ToLower().Contains(searchLower) == true ||
                       cable.GetFromEquipmentName()?.ToLower().Contains(searchLower) == true;
            }
            return false;
        }

        /// <summary>
        /// Show cable in Revit 3D view
        /// </summary>
        private void ShowInRevit(CableModel cable)
        {
            try
            {
                if (cable == null) return;

                IsLoading = true;
                StatusMessage = $"Showing cable {cable.CableRef} in Revit...";

                // Store cable for external event processing
                SharedData.SelectedCableForProcessing = cable;
                SharedData.ProcessingAction = "ShowInRevit";

                // Request external event
                MakeRequest(RequestId.ShowInRevit);

                StatusMessage = $"3D view creation requested for cable {cable.CableRef}.";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error showing cable in Revit: {ex.Message}";
                _logger?.Log($"Failed to show cable in Revit: {ex.Message}", LogType.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Clear search text
        /// </summary>
        private void ClearSearch()
        {
            SearchText = string.Empty;
        }

        /// <summary>
        /// Close the window
        /// </summary>
        private void Close()
        {
            CloseRequested?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// Navigate back to tray segments page
        /// </summary>
        private void NavigateBack()
        {
            // This will be handled by the parent ViewModel through event or navigation service
            CloseRequested?.Invoke(this, EventArgs.Empty);
        }

        #endregion
    }
}
