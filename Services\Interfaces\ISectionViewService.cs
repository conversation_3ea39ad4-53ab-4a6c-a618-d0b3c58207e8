using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using MEP.TraylorSwift.Models;
using System.Collections.Generic;

namespace MEP.TraylorSwift.Services.Interfaces
{
    /// <summary>
    /// Service for creating and managing section views of cable trays
    /// </summary>
    public interface ISectionViewService
    {
        #region Section View Creation

        /// <summary>
        /// Create a section view for a cable tray segment
        /// </summary>
        /// <param name="traySegment">Tray segment to create section view for</param>
        /// <param name="sectionName">Name for the section view</param>
        /// <returns>Created section view or null if creation failed</returns>
        ViewSection CreateTraySectionView(CableTraySegmentModel traySegment, string sectionName);

        /// <summary>
        /// Create section views for multiple tray segments
        /// </summary>
        /// <param name="traySegments">Tray segments to create section views for</param>
        /// <param name="options">Section view options</param>
        /// <returns>List of created section views</returns>
        List<ViewSection> CreateMultipleSectionViews(List<CableTraySegmentModel> traySegments, SectionViewOptions options);

        /// <summary>
        /// Create a section view at a specific location and direction
        /// </summary>
        /// <param name="origin">Origin point for section</param>
        /// <param name="direction">Direction of section cut</param>
        /// <param name="width">Width of section box</param>
        /// <param name="height">Height of section box</param>
        /// <param name="viewName">Name for the section view</param>
        /// <returns>Created section view or null if creation failed</returns>
        ViewSection CreateSectionViewAtLocation(XYZ origin, XYZ direction, double width, double height, string viewName);

        #endregion

        #region Section View Configuration

        /// <summary>
        /// Configure section view properties and annotations
        /// </summary>
        /// <param name="sectionView">Section view to configure</param>
        /// <param name="traySegment">Associated tray segment</param>
        /// <param name="config">Configuration settings</param>
        /// <returns>True if configuration was successful</returns>
        bool ConfigureSectionView(ViewSection sectionView, CableTraySegmentModel traySegment, SectionViewConfiguration config);

        /// <summary>
        /// Update section view to show latest cable information
        /// </summary>
        /// <param name="sectionView">Section view to update</param>
        /// <param name="traySegment">Updated tray segment data</param>
        /// <returns>True if update was successful</returns>
        bool UpdateSectionViewContent(ViewSection sectionView, CableTraySegmentModel traySegment);

        #endregion

        #region Section View Management

        /// <summary>
        /// Find existing section views for a tray segment
        /// </summary>
        /// <param name="traySegment">Tray segment to find section views for</param>
        /// <returns>List of existing section views</returns>
        List<ViewSection> FindExistingSectionViews(CableTraySegmentModel traySegment);

        /// <summary>
        /// Delete a section view
        /// </summary>
        /// <param name="sectionView">Section view to delete</param>
        /// <returns>True if deletion was successful</returns>
        bool DeleteSectionView(ViewSection sectionView);

        /// <summary>
        /// Get all section views in the document
        /// </summary>
        /// <returns>List of all section views</returns>
        List<ViewSection> GetAllSectionViews();

        #endregion

        #region Validation

        /// <summary>
        /// Validate section view creation requirements
        /// </summary>
        /// <param name="traySegment">Tray segment to validate</param>
        /// <returns>List of validation issues</returns>
        List<string> ValidateSectionViewCreation(CableTraySegmentModel traySegment);

        /// <summary>
        /// Check if section view creation is supported for the tray type
        /// </summary>
        /// <param name="cableTray">Cable tray to check</param>
        /// <returns>True if section view creation is supported</returns>
        bool IsSectionViewSupportedForTray(CableTray cableTray);

        #endregion
    }
}
