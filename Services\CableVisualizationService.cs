﻿using Autodesk.Revit.DB;
using BecaActivityLogger.CoreLogic.Data;
using MEP.TraylorSwift.Models;
using MEP.TraylorSwift.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using Color = Autodesk.Revit.DB.Color;

namespace MEP.TraylorSwift.Services
{
    /// <summary>
    /// Service for creating and managing cable visualization in Revit 3D views
    /// </summary>
    public class CableVisualizationService : ICableVisualizationService
    {
        #region Fields

        private readonly Document _document;
        private readonly BecaActivityLoggerData _logger;
        private const string CABLE_LINE_PREFIX = "TS_CableLine_";

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the cable visualization service
        /// </summary>
        /// <param name="document">Revit document</param>
        /// <param name="logger">Logger instance</param>
        public CableVisualizationService(Document document, BecaActivityLoggerData logger)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
            _logger = logger;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Create purple cable lines from cable route points in a 3D view
        /// </summary>
        public List<DirectShape> CreateCableLines(View3D view3D, CableModel cable)
        {
            var cableLines = new List<DirectShape>();

            try
            {
                if (cable?.RoutePoints == null)
                {
                    _logger?.Log($"Cable {cable?.CableRef ?? "Unknown"} has null RoutePoints", LogType.Warning);
                    return cableLines;
                }

                _logger?.Log($"Creating cable lines for cable {cable.CableRef} with {cable.RoutePoints.Count} route points", LogType.Information);

                // Create line segments between consecutive route points
                for (int i = 1; i < cable.RoutePoints.Count; i++)
                {
                    try
                    {
                        var startPoint = cable.RoutePoints[i - 1];
                        var endPoint = cable.RoutePoints[i];

                        // Skip zero-length segments
                        if (startPoint.DistanceTo(endPoint) < 0.01) // Less than ~1/8 inch
                        {
                            _logger?.Log($"Skipping zero-length segment {i} for cable {cable.CableRef}", LogType.Information);
                            continue;
                        }

                        // Create line geometry
                        var line = Line.CreateBound(startPoint, endPoint);
                        var curves = new List<Curve> { line };

                        // Create DirectShape for the line
                        var directShape = CreateDirectShapeFromCurves(curves, cable.CableRef, i - 1);

                        if (directShape != null)
                        {
                            cableLines.Add(directShape);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.Log($"Failed to create cable line segment {i} for cable {cable.CableRef}: {ex.Message}", LogType.Warning);
                    }
                }

                _logger?.Log($"Successfully created {cableLines.Count} cable line segments for cable {cable.CableRef}", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error creating cable lines: {ex.Message}", LogType.Error);
            }

            return cableLines;
        }

        /// <summary>
        /// Remove all existing cable lines from the specified view
        /// </summary>
        public void ClearExistingCableLines(View3D view3D, string userName)
        {
            try
            {
                // Find all DirectShape elements with cable line naming pattern
                var collector = new FilteredElementCollector(_document)
                    .OfClass(typeof(DirectShape))
                    .Cast<DirectShape>()
                    .Where(ds => ds.Name.StartsWith(CABLE_LINE_PREFIX))
                    .ToList();

                if (collector.Any())
                {
                    var elementsToDelete = collector.Select(ds => ds.Id).ToList();
                    _document.Delete(elementsToDelete);

                    _logger?.Log($"Cleared {elementsToDelete.Count} existing cable line elements", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error clearing existing cable lines: {ex.Message}", LogType.Warning);
            }
        }

        

        /// <summary>
        /// Set graphics overrides for cable run elements in the view
        /// </summary>
        public void SetCableLineGraphicsOverrides(View3D view3D, List<DirectShape> cableLineElements)
        {
            try
            {
                if (cableLineElements == null || !cableLineElements.Any()) return;

                foreach (var element in cableLineElements)
                {
                    // Create graphics override settings
                    var overrideSettings = new OverrideGraphicSettings();

                    // Set line color to purple
                    overrideSettings.SetProjectionLineColor(new Color(128, 0, 128));
                    overrideSettings.SetProjectionLineWeight(3); // Thicker line

                    // Apply override to the element in this view
                    view3D.SetElementOverrides(element.Id, overrideSettings);
                }

                _logger?.Log($"Applied graphics overrides to {cableLineElements.Count} cable line elements", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error setting cable line graphics overrides: {ex.Message}", LogType.Warning);
            }
        }

        /// <summary>
        /// Calculate bounding box for cable route points
        /// </summary>
        public BoundingBoxXYZ CalculateCableRouteBoundingBox(List<XYZ> routePoints, double margin = 5.0)
        {
            try
            {
                if (routePoints == null || !routePoints.Any())
                    return null;

                var min = new XYZ(
                    routePoints.Min(p => p.X),
                    routePoints.Min(p => p.Y),
                    routePoints.Min(p => p.Z)
                );

                var max = new XYZ(
                    routePoints.Max(p => p.X),
                    routePoints.Max(p => p.Y),
                    routePoints.Max(p => p.Z)
                );

                // Add margin
                var expandedBbox = new BoundingBoxXYZ
                {
                    Min = new XYZ(min.X - margin, min.Y - margin, min.Z - margin),
                    Max = new XYZ(max.X + margin, max.Y + margin, max.Z + margin)
                };

                return expandedBbox;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error calculating cable route bounding box: {ex.Message}", LogType.Warning);
                return null;
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Create DirectShape element from curves
        /// </summary>
        private DirectShape CreateDirectShapeFromCurves(List<Curve> curves, string cableRef, int segmentIndex)
        {
            try
            {
                var directShape = DirectShape.CreateElement(_document, new ElementId(BuiltInCategory.OST_GenericModel));

                // Set name for identification
                directShape.Name = $"{CABLE_LINE_PREFIX}{cableRef}_{segmentIndex}";

                // Create geometry from curves
                var geometryObjects = curves.Cast<GeometryObject>().ToList();
                directShape.SetShape(geometryObjects);

                return directShape;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error creating DirectShape from curves: {ex.Message}", LogType.Warning);
                return null;
            }
        }

        #endregion
    }
}
