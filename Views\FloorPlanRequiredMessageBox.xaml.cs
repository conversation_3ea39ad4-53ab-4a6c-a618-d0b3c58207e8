﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using Application = System.Windows.Application;

namespace MEP.TraylorSwift.Views
{
    /// <summary>
    /// Interaction logic for FloorPlanRequiredMessageBox.xaml
    /// </summary>
    public partial class FloorPlanRequiredMessageBox : Window
    {
        /// <summary>
        /// Gets whether the "Don't show again" checkbox was checked
        /// </summary>
        public bool DontShowAgain { get; private set; }

        public FloorPlanRequiredMessageBox()
        {
            InitializeComponent();

            // Set owner to main application window if available
            if (Application.Current.MainWindow != null && Application.Current.MainWindow != this)
            {
                Owner = Application.Current.MainWindow;
            }
        }

        /// <summary>
        /// Handle OK button click
        /// </summary>
        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            // Store the checkbox state
            DontShowAgain = DontShowAgainCheckBox.IsChecked == true;

            // Close the dialog with OK result
            DialogResult = true;
            Close();
        }
    }
}
