using Autodesk.Revit.DB;
using MEP.TraylorSwift.Models;
using System;
using System.Collections.Generic;

namespace MEP.TraylorSwift.Services.Interfaces
{
    /// <summary>
    /// Service for spatial indexing and efficient geometric queries
    /// </summary>
    public interface ISpatialIndexService
    {
        #region Index Management

        /// <summary>
        /// Create a spatial index for cable tray segments
        /// </summary>
        /// <param name="traySegments">Tray segments to index</param>
        /// <param name="indexType">Type of spatial index to create</param>
        /// <returns>Spatial index instance</returns>
        ISpatialIndex<CableTraySegmentModel> CreateTraySegmentIndex(List<CableTraySegmentModel> traySegments, SpatialIndexType indexType = SpatialIndexType.RTree);

        /// <summary>
        /// Create a spatial index for cables
        /// </summary>
        /// <param name="cables">Cables to index</param>
        /// <param name="indexType">Type of spatial index to create</param>
        /// <returns>Spatial index instance</returns>
        ISpatialIndex<CableModel> CreateCableIndex(List<CableModel> cables, SpatialIndexType indexType = SpatialIndexType.RTree);

        /// <summary>
        /// Create a spatial index for electrical equipment
        /// </summary>
        /// <param name="equipment">Equipment elements to index</param>
        /// <param name="indexType">Type of spatial index to create</param>
        /// <returns>Spatial index instance</returns>
        ISpatialIndex<Element> CreateEquipmentIndex(List<Element> equipment, SpatialIndexType indexType = SpatialIndexType.RTree);

        /// <summary>
        /// Update an existing spatial index with new or modified items
        /// </summary>
        /// <typeparam name="T">Type of items in the index</typeparam>
        /// <param name="index">Spatial index to update</param>
        /// <param name="items">Items to add or update</param>
        /// <returns>True if index was updated successfully</returns>
        bool UpdateSpatialIndex<T>(ISpatialIndex<T> index, List<T> items) where T : class;

        /// <summary>
        /// Clear and rebuild a spatial index
        /// </summary>
        /// <typeparam name="T">Type of items in the index</typeparam>
        /// <param name="index">Spatial index to rebuild</param>
        /// <param name="items">Items to rebuild index with</param>
        /// <returns>True if index was rebuilt successfully</returns>
        bool RebuildSpatialIndex<T>(ISpatialIndex<T> index, List<T> items) where T : class;

        #endregion

        #region Spatial Queries

        /// <summary>
        /// Find items within a bounding box
        /// </summary>
        /// <typeparam name="T">Type of items to search for</typeparam>
        /// <param name="index">Spatial index to search</param>
        /// <param name="boundingBox">Search bounding box</param>
        /// <returns>List of items within the bounding box</returns>
        List<T> FindItemsInBoundingBox<T>(ISpatialIndex<T> index, BoundingBoxXYZ boundingBox) where T : class;

        /// <summary>
        /// Find items within a specified distance from a point
        /// </summary>
        /// <typeparam name="T">Type of items to search for</typeparam>
        /// <param name="index">Spatial index to search</param>
        /// <param name="point">Search center point</param>
        /// <param name="distance">Search radius</param>
        /// <returns>List of items within the specified distance</returns>
        List<T> FindItemsNearPoint<T>(ISpatialIndex<T> index, XYZ point, double distance) where T : class;

        /// <summary>
        /// Find items that intersect with a line segment
        /// </summary>
        /// <typeparam name="T">Type of items to search for</typeparam>
        /// <param name="index">Spatial index to search</param>
        /// <param name="startPoint">Start point of line segment</param>
        /// <param name="endPoint">End point of line segment</param>
        /// <param name="tolerance">Intersection tolerance</param>
        /// <returns>List of items that intersect the line segment</returns>
        List<T> FindItemsIntersectingLine<T>(ISpatialIndex<T> index, XYZ startPoint, XYZ endPoint, double tolerance = 0.1) where T : class;

        /// <summary>
        /// Find items that intersect with a curve
        /// </summary>
        /// <typeparam name="T">Type of items to search for</typeparam>
        /// <param name="index">Spatial index to search</param>
        /// <param name="curve">Curve to test intersection with</param>
        /// <param name="tolerance">Intersection tolerance</param>
        /// <returns>List of items that intersect the curve</returns>
        List<T> FindItemsIntersectingCurve<T>(ISpatialIndex<T> index, Curve curve, double tolerance = 0.1) where T : class;

        /// <summary>
        /// Find the nearest items to a point
        /// </summary>
        /// <typeparam name="T">Type of items to search for</typeparam>
        /// <param name="index">Spatial index to search</param>
        /// <param name="point">Search point</param>
        /// <param name="maxCount">Maximum number of items to return</param>
        /// <returns>List of nearest items sorted by distance</returns>
        List<SpatialQueryResult<T>> FindNearestItems<T>(ISpatialIndex<T> index, XYZ point, int maxCount = 10) where T : class;

        #endregion

        #region Specialized Queries for Cable Trays

        /// <summary>
        /// Find cable trays that could contain a specific cable
        /// </summary>
        /// <param name="trayIndex">Spatial index of tray segments</param>
        /// <param name="cable">Cable to find trays for</param>
        /// <param name="tolerance">Intersection tolerance</param>
        /// <returns>List of candidate tray segments</returns>
        List<CableTraySegmentModel> FindCandidateTraysForCable(ISpatialIndex<CableTraySegmentModel> trayIndex, CableModel cable, double tolerance = 0.1);

        /// <summary>
        /// Find cables that could be contained in a specific tray
        /// </summary>
        /// <param name="cableIndex">Spatial index of cables</param>
        /// <param name="traySegment">Tray segment to find cables for</param>
        /// <param name="tolerance">Intersection tolerance</param>
        /// <returns>List of candidate cables</returns>
        List<CableModel> FindCandidateCablesForTray(ISpatialIndex<CableModel> cableIndex, CableTraySegmentModel traySegment, double tolerance = 0.1);

        /// <summary>
        /// Find connected cable trays
        /// </summary>
        /// <param name="trayIndex">Spatial index of tray segments</param>
        /// <param name="traySegment">Source tray segment</param>
        /// <param name="connectionTolerance">Connection tolerance</param>
        /// <returns>List of connected tray segments</returns>
        List<CableTraySegmentModel> FindConnectedTrays(ISpatialIndex<CableTraySegmentModel> trayIndex, CableTraySegmentModel traySegment, double connectionTolerance = 0.1);

        /// <summary>
        /// Find electrical equipment near a cable tray
        /// </summary>
        /// <param name="equipmentIndex">Spatial index of electrical equipment</param>
        /// <param name="traySegment">Tray segment to search near</param>
        /// <param name="searchDistance">Search distance</param>
        /// <returns>List of nearby electrical equipment</returns>
        List<Element> FindEquipmentNearTray(ISpatialIndex<Element> equipmentIndex, CableTraySegmentModel traySegment, double searchDistance = 5.0);

        #endregion

        #region Performance Optimization

        /// <summary>
        /// Optimize spatial index for better query performance
        /// </summary>
        /// <typeparam name="T">Type of items in the index</typeparam>
        /// <param name="index">Spatial index to optimize</param>
        /// <returns>True if optimization was successful</returns>
        bool OptimizeSpatialIndex<T>(ISpatialIndex<T> index) where T : class;

        /// <summary>
        /// Get performance statistics for a spatial index
        /// </summary>
        /// <typeparam name="T">Type of items in the index</typeparam>
        /// <param name="index">Spatial index to analyze</param>
        /// <returns>Performance statistics</returns>
        SpatialIndexStatistics GetIndexStatistics<T>(ISpatialIndex<T> index) where T : class;

        /// <summary>
        /// Benchmark spatial index performance
        /// </summary>
        /// <typeparam name="T">Type of items in the index</typeparam>
        /// <param name="index">Spatial index to benchmark</param>
        /// <param name="testQueries">Test queries to run</param>
        /// <returns>Benchmark results</returns>
        SpatialIndexBenchmark BenchmarkIndex<T>(ISpatialIndex<T> index, List<SpatialQuery> testQueries) where T : class;

        #endregion

        #region Index Persistence

        /// <summary>
        /// Save spatial index to file for later loading
        /// </summary>
        /// <typeparam name="T">Type of items in the index</typeparam>
        /// <param name="index">Spatial index to save</param>
        /// <param name="filePath">File path to save to</param>
        /// <returns>True if index was saved successfully</returns>
        bool SaveSpatialIndex<T>(ISpatialIndex<T> index, string filePath) where T : class;

        /// <summary>
        /// Load spatial index from file
        /// </summary>
        /// <typeparam name="T">Type of items in the index</typeparam>
        /// <param name="filePath">File path to load from</param>
        /// <returns>Loaded spatial index or null if loading failed</returns>
        ISpatialIndex<T> LoadSpatialIndex<T>(string filePath) where T : class;

        /// <summary>
        /// Check if a saved spatial index is still valid
        /// </summary>
        /// <param name="filePath">File path of saved index</param>
        /// <param name="documentTimestamp">Timestamp of current document</param>
        /// <returns>True if saved index is still valid</returns>
        bool IsSavedIndexValid(string filePath, DateTime documentTimestamp);

        #endregion
    }

    /// <summary>
    /// Generic spatial index interface
    /// </summary>
    /// <typeparam name="T">Type of items stored in the index</typeparam>
    public interface ISpatialIndex<T> where T : class
    {
        /// <summary>
        /// Add an item to the spatial index
        /// </summary>
        /// <param name="item">Item to add</param>
        /// <param name="boundingBox">Bounding box of the item</param>
        void Add(T item, BoundingBoxXYZ boundingBox);

        /// <summary>
        /// Remove an item from the spatial index
        /// </summary>
        /// <param name="item">Item to remove</param>
        /// <returns>True if item was removed</returns>
        bool Remove(T item);

        /// <summary>
        /// Update an item's position in the spatial index
        /// </summary>
        /// <param name="item">Item to update</param>
        /// <param name="newBoundingBox">New bounding box of the item</param>
        /// <returns>True if item was updated</returns>
        bool Update(T item, BoundingBoxXYZ newBoundingBox);

        /// <summary>
        /// Clear all items from the spatial index
        /// </summary>
        void Clear();

        /// <summary>
        /// Get the number of items in the spatial index
        /// </summary>
        int Count { get; }

        /// <summary>
        /// Get the type of spatial index
        /// </summary>
        SpatialIndexType IndexType { get; }

        /// <summary>
        /// Query items within a bounding box
        /// </summary>
        /// <param name="boundingBox">Query bounding box</param>
        /// <returns>Items within the bounding box</returns>
        List<T> Query(BoundingBoxXYZ boundingBox);

        /// <summary>
        /// Query items within a distance from a point
        /// </summary>
        /// <param name="point">Query point</param>
        /// <param name="distance">Query distance</param>
        /// <returns>Items within the distance</returns>
        List<T> Query(XYZ point, double distance);
    }

    /// <summary>
    /// Result of a spatial query with distance information
    /// </summary>
    /// <typeparam name="T">Type of item</typeparam>
    public class SpatialQueryResult<T> where T : class
    {
        public T Item { get; set; }
        public double Distance { get; set; }
        public BoundingBoxXYZ BoundingBox { get; set; }
        public XYZ ClosestPoint { get; set; }
    }

    /// <summary>
    /// Performance statistics for a spatial index
    /// </summary>
    public class SpatialIndexStatistics
    {
        public int ItemCount { get; set; }
        public int NodeCount { get; set; }
        public int MaxDepth { get; set; }
        public double AverageQueryTime { get; set; }
        public double IndexSize { get; set; }
        public double BuildTime { get; set; }
        public SpatialIndexType IndexType { get; set; }
    }

    /// <summary>
    /// Benchmark results for a spatial index
    /// </summary>
    public class SpatialIndexBenchmark
    {
        public int TotalQueries { get; set; }
        public double TotalTime { get; set; }
        public double AverageTime { get; set; }
        public double MinTime { get; set; }
        public double MaxTime { get; set; }
        public int TotalResults { get; set; }
        public double AverageResults { get; set; }
        public List<double> QueryTimes { get; set; }

        public SpatialIndexBenchmark()
        {
            QueryTimes = new List<double>();
        }
    }

    /// <summary>
    /// Spatial query for benchmarking
    /// </summary>
    public class SpatialQuery
    {
        public SpatialQueryType QueryType { get; set; }
        public BoundingBoxXYZ BoundingBox { get; set; }
        public XYZ Point { get; set; }
        public double Distance { get; set; }
        public XYZ StartPoint { get; set; }
        public XYZ EndPoint { get; set; }
        public string Description { get; set; }
    }

    /// <summary>
    /// Types of spatial queries
    /// </summary>
    public enum SpatialQueryType
    {
        BoundingBox,
        PointDistance,
        LineIntersection,
        NearestNeighbor
    }
}
