﻿<Page
    x:Class="MEP.TraylorSwift.Views.Pages.TraySegmentsPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:MEP.TraylorSwift.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.TraylorSwift.Views.Pages"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="TraySegmentsPage"
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d">
    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter" />
            <converters:CountToVisibilityConverter x:Key="CountToVisibilityConverter" />

        </ResourceDictionary>
    </Page.Resources>

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="200" />
        </Grid.RowDefinitions>

        <!--  Tray Segments List  -->
        <materialDesign:Card Grid.Row="0" Padding="10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="Cable Tray Segments" />

                <DataGrid
                    Grid.Row="1"
                    Margin="0,10,0,0"
                    AutoGenerateColumns="False"
                    CanUserAddRows="False"
                    CanUserDeleteRows="False"
                    ItemsSource="{Binding TraySegmentsView}"
                    SelectedItem="{Binding SelectedTraySegment}"
                    SelectionMode="Single">
                    <DataGrid.Columns>
                        <DataGridTextColumn 
                            Width="100" 
                            Binding="{Binding TrayRef}"
                            Header="Tray Ref&#x0a;"
                            IsReadOnly="True" />
                        <DataGridTextColumn
                            Width="80"
                            Binding="{Binding Width, StringFormat=F2}"
                            Header="Width&#x0a;"
                            IsReadOnly="True" />
                        <DataGridTextColumn
                            Width="80"
                            Binding="{Binding Cables.Count}"
                            Header="Cables&#x0a;"
                            IsReadOnly="True" />
                        <DataGridTextColumn 
                            Width="100"
                            Binding="{Binding Capacity, StringFormat=F1}"
                            Header="Capacity %&#x0a;"
                            IsReadOnly="True" />
                        <DataGridTextColumn
                            Width="80"
                            Binding="{Binding Weight, StringFormat=F1}"
                            Header="Weight&#x0a;"
                            IsReadOnly="True" />
                        <DataGridTemplateColumn Width="120" Header="Configure&#x0a;">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button
                                        Height="30" Margin="-5,0,0,0"
                                        HorizontalAlignment="Center"
                                        Background="Transparent"
                                        BorderThickness="0"
                                        Command="{Binding DataContext.ConfigureTraySegmentCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                        CommandParameter="{Binding}"
                                        Content="{materialDesign:PackIcon Kind=Cog,
                                                                          Size=20}"
                                        FontSize="10"
                                        Foreground="#12A8B2" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTextColumn
                            Width="110"
                            Binding="{Binding FloorPlan.Name, StringFormat=F1}"
                            Header="Floor Plan&#x0a;"
                            IsReadOnly="True" />
                        <DataGridTemplateColumn Width="120" Header="Open&#x0a;Floor Plan">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button
                                        Height="30" Padding="-5,0,0,0"
                                        Background="Transparent"
                                        BorderThickness="0"
                                        Command="{Binding DataContext.OpenFloorPlanCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                        CommandParameter="{Binding}"
                                        Content="{materialDesign:PackIcon Kind=FloorPlan,
                                                                          Size=20}"
                                        FontSize="10"
                                        Foreground="#12A8B2"
                                        Visibility="{Binding Converter={StaticResource NullToVisibilityConverter}}" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="120" Header="Remove&#x0a;">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button
                                        Height="30"
                                        HorizontalAlignment="Center"
                                        Background="Transparent"
                                        BorderThickness="0"
                                        Command="{Binding DataContext.RemoveTraySegmentCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                        CommandParameter="{Binding}"
                                        Content="{materialDesign:PackIcon Kind=TrashCan,
                                                                          Size=20}"
                                        FontSize="10"
                                        Foreground="Red" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>

        <!--  Splitter  -->
        <GridSplitter
            Grid.Row="1"
            Height="5"
            HorizontalAlignment="Stretch" />

        <!--  Selected Tray Details  -->
        <materialDesign:Card Grid.Row="2" Padding="10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="Selected Tray Details" />

                <ScrollViewer
                    Grid.Row="1"
                    Margin="0,10,0,0"
                    VerticalScrollBarVisibility="Auto">
                    <StackPanel DataContext="{Binding SelectedTraySegment}">
                        <TextBlock Text="{Binding TrayRef, StringFormat='Tray Reference: {0}'}" Visibility="{Binding Converter={StaticResource NullToVisibilityConverter}}" />
                        <TextBlock Text="{Binding Width, StringFormat='Width: {0:F2} ft'}" Visibility="{Binding Converter={StaticResource NullToVisibilityConverter}}" />
                        <TextBlock Text="{Binding Height, StringFormat='Height: {0:F2} ft'}" Visibility="{Binding Converter={StaticResource NullToVisibilityConverter}}" />
                        <TextBlock Text="{Binding Capacity, StringFormat='Capacity: {0:F1}%'}" Visibility="{Binding Converter={StaticResource NullToVisibilityConverter}}" />
                        <TextBlock Text="{Binding Cables.Count, StringFormat='Number of Cables: {0}'}" Visibility="{Binding Converter={StaticResource NullToVisibilityConverter}}" />

                        <TextBlock
                            Margin="0,10,0,5"
                            Text="Cables:"
                            Visibility="{Binding Cables.Count, Converter={StaticResource CountToVisibilityConverter}}" />

                        <ItemsControl ItemsSource="{Binding Cables}" Visibility="{Binding Cables.Count, Converter={StaticResource CountToVisibilityConverter}}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <TextBlock Margin="10,0,0,2" Text="{Binding CableRef}" />
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </ScrollViewer>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Page>
