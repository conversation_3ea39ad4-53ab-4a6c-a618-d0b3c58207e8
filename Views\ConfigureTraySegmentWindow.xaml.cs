using MEP.TraylorSwift.ViewModels;
using System.Windows;

namespace MEP.TraylorSwift.Views
{
    /// <summary>
    /// Interaction logic for ConfigureTraySegmentWindow.xaml
    /// </summary>
    public partial class ConfigureTraySegmentWindow : Window
    {
        #region Fields

        private ConfigureTraySegmentViewModel _viewModel;

        #endregion

        #region Properties

        /// <summary>
        /// The ViewModel for this window
        /// </summary>
        public ConfigureTraySegmentViewModel ViewModel
        {
            get => _viewModel;
            set
            {
                if (_viewModel != value)
                {
                    _viewModel = value;
                    DataContext = _viewModel;
                }
            }
        }

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the ConfigureTraySegmentWindow
        /// </summary>
        public ConfigureTraySegmentWindow()
        {
            InitializeComponent();
        }

        /// <summary>
        /// Initialize the ConfigureTraySegmentWindow with a ViewModel
        /// </summary>
        /// <param name="viewModel">The ViewModel to use</param>
        public ConfigureTraySegmentWindow(ConfigureTraySegmentViewModel viewModel) : this()
        {
            ViewModel = viewModel;
            
            // Subscribe to close request
            if (viewModel != null)
            {
                viewModel.CloseRequested += (s, e) => Close();
            }
        }

        #endregion
    }
}
