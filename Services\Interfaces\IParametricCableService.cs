using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using MEP.TraylorSwift.Models;
using System.Collections.Generic;

namespace MEP.TraylorSwift.Services.Interfaces
{
    /// <summary>
    /// Service for managing BecaParametricCable family instances and their parameters
    /// </summary>
    public interface IParametricCableService
    {
        #region Parametric Cable Creation

        /// <summary>
        /// Create a parametric cable for a cable tray segment
        /// </summary>
        /// <param name="cableTray">Cable tray to place parametric cable on</param>
        /// <param name="traySegment">Tray segment data</param>
        /// <returns>Created parametric cable instance or null if creation failed</returns>
        FamilyInstance CreateParametricCable(CableTray cableTray, CableTraySegmentModel traySegment);

        /// <summary>
        /// Create parametric cables for multiple tray segments
        /// </summary>
        /// <param name="traySegments">Tray segments to create parametric cables for</param>
        /// <param name="options">Parametric cable options</param>
        /// <returns>List of created parametric cable instances</returns>
        List<FamilyInstance> CreateMultipleParametricCables(List<CableTraySegmentModel> traySegments, ParametricCableOptions options);

        /// <summary>
        /// Create a parametric cable along a specific path
        /// </summary>
        /// <param name="path">Path for the parametric cable</param>
        /// <param name="config">Parametric cable configuration</param>
        /// <returns>Created parametric cable instance or null if creation failed</returns>
        FamilyInstance CreateParametricCableAlongPath(Curve path, ParametricCableConfiguration config);

        #endregion

        #region Parametric Cable Configuration

        /// <summary>
        /// Configure parametric cable parameters based on tray segment data
        /// </summary>
        /// <param name="cableInstance">Parametric cable instance to configure</param>
        /// <param name="traySegment">Tray segment data</param>
        /// <param name="config">Optional configuration settings</param>
        /// <returns>True if configuration was successful</returns>
        bool ConfigureCableParameters(FamilyInstance cableInstance, CableTraySegmentModel traySegment, ParametricCableConfiguration config = null);

        /// <summary>
        /// Update parametric cable with latest tray segment data
        /// </summary>
        /// <param name="cableInstance">Parametric cable instance to update</param>
        /// <param name="traySegment">Updated tray segment data</param>
        /// <returns>True if update was successful</returns>
        bool UpdateParametricCable(FamilyInstance cableInstance, CableTraySegmentModel traySegment);

        /// <summary>
        /// Set cable visibility and display options
        /// </summary>
        /// <param name="cableInstance">Parametric cable instance</param>
        /// <param name="options">Visibility options</param>
        /// <returns>True if visibility was set successfully</returns>
        bool SetCableVisibility(FamilyInstance cableInstance, CableVisibilityOptions options);

        #endregion

        #region Parametric Cable Management

        /// <summary>
        /// Find existing parametric cables for a tray segment
        /// </summary>
        /// <param name="traySegment">Tray segment to find parametric cables for</param>
        /// <returns>List of existing parametric cables</returns>
        List<FamilyInstance> FindExistingParametricCables(CableTraySegmentModel traySegment);

        /// <summary>
        /// Delete a parametric cable
        /// </summary>
        /// <param name="cableInstance">Parametric cable instance to delete</param>
        /// <returns>True if deletion was successful</returns>
        bool DeleteParametricCable(FamilyInstance cableInstance);

        /// <summary>
        /// Get all parametric cables in the document
        /// </summary>
        /// <returns>List of all parametric cables</returns>
        List<FamilyInstance> GetAllParametricCables();

        #endregion

        #region Cable Analysis

        /// <summary>
        /// Analyze parametric cable performance and optimization
        /// </summary>
        /// <param name="cableInstance">Parametric cable instance to analyze</param>
        /// <param name="traySegment">Associated tray segment</param>
        /// <returns>Analysis results</returns>
        ParametricCableAnalysis AnalyzeCablePerformance(FamilyInstance cableInstance, CableTraySegmentModel traySegment);

        /// <summary>
        /// Generate cable routing optimization suggestions
        /// </summary>
        /// <param name="traySegments">Tray segments to analyze</param>
        /// <returns>List of optimization suggestions</returns>
        List<string> GenerateRoutingOptimizations(List<CableTraySegmentModel> traySegments);

        #endregion

        #region Validation

        /// <summary>
        /// Validate parametric cable creation requirements
        /// </summary>
        /// <param name="traySegment">Tray segment to validate</param>
        /// <returns>List of validation issues</returns>
        List<string> ValidateParametricCableCreation(CableTraySegmentModel traySegment);

        /// <summary>
        /// Check if parametric cable creation is supported for the tray type
        /// </summary>
        /// <param name="cableTray">Cable tray to check</param>
        /// <returns>True if parametric cable creation is supported</returns>
        bool IsParametricCableSupportedForTray(CableTray cableTray);

        #endregion
    }
}
