﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.UI;
using BecaCommand;
using MEP.TraylorSwift.Handlers;

namespace MEP.TraylorSwift.RevitCommands
{
    [Transaction(TransactionMode.Manual)]
    public class TraylorSwiftCommand : BecaBaseCommand
    {
        public override Result ExecuteBecaCommand(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            UIApplication uiapp = commandData.Application;
            UIDocument uidoc = uiapp.ActiveUIDocument;
            Document doc = uidoc.Document;

            _taskLogger.PreTaskStart();

            // Launch WPF MVVM interface using modeless architecture
            ModelessMainWindowHandler.ShowWindow(uidoc, _taskLogger);

            return Result.Succeeded;
        }

        public override string GetAddinAuthor()
        {
            return "Tristan Balme, Firza Utama";
        }

        public override string GetAddinName()
        {
            return AddinNames.TraylorSwift.Value;
        }

        public override string GetCommandSubName()
        {
            return string.Empty;
        }
    }
}
