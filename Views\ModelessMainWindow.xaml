﻿<Window
    x:Class="MEP.TraylorSwift.Views.ModelessMainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:MEP.TraylorSwift.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.TraylorSwift.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:viewModels="clr-namespace:MEP.TraylorSwift.ViewModels"
    Title="Traylor Swift - Cable Tray Tagging"
    Width="1200"
    Height="800"
    MinWidth="800"
    MinHeight="600"
    mc:Ignorable="d">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <converters:InverseBooleanConverter x:Key="InverseBooleanConverter" />
            <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
            <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter" />
            <converters:CountToVisibilityConverter x:Key="CountToVisibilityConverter" />
            <converters:DoubleFormatConverter x:Key="DoubleFormatConverter" />
            <converters:PercentageConverter x:Key="PercentageConverter" />
            <converters:EnumToStringConverter x:Key="EnumToStringConverter" />
            <converters:CountToStringConverter x:Key="CountToStringConverter" />

        </ResourceDictionary>
    </Window.Resources>
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  Header  -->
        <Grid Grid.Row="0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <TextBlock
                    Margin="15,5,0,10"
                    Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                    Text="TRAYLOR SWIFT" />
            </StackPanel>

            <Border
                Grid.Column="1"
                Width="50"
                Height="50"
                Margin="10,0,20,20">
                <Image
                    RenderOptions.BitmapScalingMode="HighQuality"
                    RenderOptions.EdgeMode="Aliased"
                    SnapsToDevicePixels="True"
                    Source="/MEP.TraylorSwift;component/Views/TraylorSwift.png"
                    Stretch="Uniform"
                    StretchDirection="Both"
                    UseLayoutRounding="True" />
            </Border>
        </Grid>

        <Separator
            Grid.Row="0"
            Margin="10,45,15,0"
            Background="#FFCE00">
            <Separator.LayoutTransform>
                <ScaleTransform ScaleY="3.5" />
            </Separator.LayoutTransform>
        </Separator>

        <!--  Main Content  -->
        <Grid Grid.Row="1" Margin="0,10,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300" />
                <ColumnDefinition Width="5" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <!--  Left Panel - Controls  -->
            <Border
                Grid.Column="0"
                Padding="10"
                Background="{DynamicResource MaterialDesignPaper}"
                CornerRadius="4">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!--  Level Filter Section  -->
                        <materialDesign:Card Margin="0,0,0,10" Padding="10">
                            <StackPanel>
                                <TextBlock
                                    FontFamily="Arial"
                                    FontSize="15"
                                    FontWeight="SemiBold"
                                    Text="Level Filter" />

                                <ComboBox
                                    Margin="0,5,0,0"
                                    materialDesign:HintAssist.Hint="Select Level"
                                    ItemsSource="{Binding AvailableLevels}"
                                    SelectedItem="{Binding SelectedLevel}" />
                            </StackPanel>
                        </materialDesign:Card>

                        <!--  Tray Operations Section  -->
                        <materialDesign:Card Margin="0,0,0,10" Padding="5">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon
                                    Width="25"
                                    Height="25"
                                    Margin="10,0,8,0"
                                    VerticalAlignment="Center"
                                    Foreground="#12A8B2"
                                    Kind="PlusCircleOutline" />
                                <Button
                                    Padding="5,2"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    Command="{Binding AddTraySegmentCommand}"
                                    Content="Add Tray Segment"
                                    Foreground="Black"
                                    Style="{StaticResource MaterialDesignRaisedButton}" />

                            </StackPanel>
                        </materialDesign:Card>

                        <!--  Cable Viewer Section  -->
                        <materialDesign:Card Margin="0,0,0,10" Padding="5">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon
                                    Width="25"
                                    Height="25"
                                    Margin="10,0,8,0"
                                    VerticalAlignment="Center"
                                    Foreground="#12A8B2"
                                    Kind="EyeArrowRight" />
                                <Button
                                    Padding="5,2"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    Command="{Binding OpenCableViewerCommand}"
                                    Content="Cable Viewer"
                                    Foreground="Black"
                                    Style="{StaticResource MaterialDesignRaisedButton}" />
                            </StackPanel>
                        </materialDesign:Card>
                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!--  Splitter  -->
            <GridSplitter
                Grid.Column="1"
                HorizontalAlignment="Stretch"
                Background="{DynamicResource MaterialDesignDivider}" />

            <!--  Right Panel - Page Navigation  -->
            <Grid Grid.Column="2" Margin="10,0,0,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="112*" />
                    <RowDefinition Height="67*" />
                </Grid.RowDefinitions>

                <!--  Navigation Header  -->
                <materialDesign:Card
                    Grid.Row="0"
                    Margin="10,0,10,10"
                    Padding="10">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>

                        <TextBlock
                            Grid.Column="0"
                            VerticalAlignment="Center"
                            Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                            Text="{Binding CurrentPageTitle}" />

                    </Grid>
                </materialDesign:Card>

                <!--  Page Content Frame  -->
                <Frame
                    x:Name="MainContentFrame"
                    Grid.Row="1"
                    Grid.RowSpan="2"
                    NavigationUIVisibility="Hidden" />
            </Grid>
        </Grid>

        <!--  Progress Bar  -->
        <ProgressBar
            Grid.Row="2"
            Margin="0,5,0,5"
            IsIndeterminate="{Binding IsLoading}"
            Visibility="{Binding ShowProgressBar, Converter={StaticResource BooleanToVisibilityConverter}}"
            Value="{Binding ProgressValue}" />

        <!--  Status Bar  -->
        <Border
            Grid.Row="3"
            Padding="10,5"
            Background="{DynamicResource MaterialDesignDivider}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <TextBlock
                    Grid.Column="0"
                    VerticalAlignment="Center"
                    Text="{Binding StatusMessage}" />

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Border
                        Grid.Column="1"
                        Width="120"
                        Margin="10">
                        <Image
                            RenderOptions.BitmapScalingMode="HighQuality"
                            RenderOptions.EdgeMode="Aliased"
                            SnapsToDevicePixels="True"
                            Source="/MEP.TraylorSwift;component/Views/BecaLogoBlack.png"
                            Stretch="Uniform"
                            StretchDirection="Both"
                            UseLayoutRounding="True" />
                    </Border>
                    <TextBlock
                        Margin="0,10,10,0"
                        VerticalAlignment="Center"
                        Style="{StaticResource MaterialDesignCaptionTextBlock}"
                        Text="Make Everyday Better" />
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
