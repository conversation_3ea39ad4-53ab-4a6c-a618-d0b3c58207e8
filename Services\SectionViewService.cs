using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using BecaActivityLogger.CoreLogic.Data;
using BecaRevitUtilities.Extensions;
using MEP.TraylorSwift.Models;
using MEP.TraylorSwift.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;

namespace MEP.TraylorSwift.Services
{
    /// <summary>
    /// Implementation of ISectionViewService for creating and managing section views
    /// </summary>
    public class SectionViewService : ISectionViewService
    {
        #region Fields

        private readonly Document _document;
        private readonly BecaActivityLoggerData _logger;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the section view service
        /// </summary>
        /// <param name="document">Revit document</param>
        /// <param name="logger">Activity logger</param>
        public SectionViewService(Document document, BecaActivityLoggerData logger)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
            _logger = logger;
        }

        #endregion

        #region Section View Creation

        /// <summary>
        /// Create a section view for a cable tray segment
        /// </summary>
        public ViewSection CreateTraySectionView(CableTraySegmentModel traySegment, string sectionName)
        {
            try
            {
                if (traySegment?.CableTray == null) return null;

                // Get tray centerline for section placement
                var centerline = GetTrayCenterline(traySegment.CableTray);
                if (centerline == null)
                {
                    _logger?.Log($"Could not get centerline for tray {traySegment.TrayRef}", LogType.Error);
                    return null;
                }

                // Calculate section box
                var sectionBox = CalculateSectionBox(traySegment, centerline);
                if (sectionBox == null)
                {
                    _logger?.Log($"Could not calculate section box for tray {traySegment.TrayRef}", LogType.Error);
                    return null;
                }

                // Get section view type
                var sectionViewType = GetSectionViewType();
                if (sectionViewType == null)
                {
                    _logger?.Log("Could not find section view type", LogType.Error);
                    return null;
                }

                // Create the section view
                var sectionView = ViewSection.CreateSection(_document, sectionViewType.Id, sectionBox);

                if (sectionView != null)
                {
                    // Configure section view properties
                    ConfigureSectionView(sectionView, traySegment, sectionName);

                    MessageBox.Show($"Created section view '{sectionName}' for tray {traySegment.TrayRef}", "Section View successfully created", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    _logger?.Log($"Created section view '{sectionName}' for tray {traySegment.TrayRef}", LogType.Information);
                    return sectionView;
                }
                else
                {
                    _logger?.Log($"Failed to create section view for tray {traySegment.TrayRef}", LogType.Error);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error creating tray section view: {ex.Message}", LogType.Error);
                return null;
            }
        }

        /// <summary>
        /// Create section views for multiple tray segments
        /// </summary>
        public List<ViewSection> CreateMultipleSectionViews(List<CableTraySegmentModel> traySegments, SectionViewOptions options)
        {
            var createdViews = new List<ViewSection>();

            try
            {
                if (traySegments == null || traySegments.Count == 0) return createdViews;

                using (var transaction = new Transaction(_document, "Create Multiple Section Views"))
                {
                    transaction.Start();

                    foreach (var traySegment in traySegments)
                    {
                        try
                        {
                            if (options.SkipExistingViews && traySegment.SectionView != null)
                                continue;

                            var sectionName = GenerateSectionViewName(traySegment, options);
                            var sectionView = CreateTraySectionView(traySegment, sectionName);
                            
                            if (sectionView != null)
                            {
                                createdViews.Add(sectionView);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger?.Log($"Failed to create section view for tray {traySegment.TrayRef}: {ex.Message}", LogType.Warning);
                        }
                    }

                    transaction.Commit();
                    _logger?.Log($"Created {createdViews.Count} section views out of {traySegments.Count} tray segments", LogType.Information);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error creating multiple section views: {ex.Message}", LogType.Error);
            }

            return createdViews;
        }

        /// <summary>
        /// Create a section view at a specific location and direction
        /// </summary>
        public ViewSection CreateSectionViewAtLocation(XYZ origin, XYZ direction, double width, double height, string viewName)
        {
            try
            {
                if (origin == null || direction == null) return null;

                // Create transform for section box
                var transform = CreateSectionTransform(origin, direction);
                var sectionBox = CreateSectionBoundingBox(transform, width, height);

                var sectionViewType = GetSectionViewType();
                if (sectionViewType == null) return null;

                using (var transaction = new Transaction(_document, "Create Section View at Location"))
                {
                    transaction.Start();

                    var sectionView = ViewSection.CreateSection(_document, sectionViewType.Id, sectionBox);
                    
                    if (sectionView != null && !string.IsNullOrEmpty(viewName))
                    {
                        try
                        {
                            sectionView.Name = viewName;
                        }
                        catch
                        {
                            // Name might not be unique, Revit will handle it
                        }
                    }

                    transaction.Commit();
                    _logger?.Log($"Created section view '{viewName}' at location", LogType.Information);
                    return sectionView;
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error creating section view at location: {ex.Message}", LogType.Error);
                return null;
            }
        }

        #endregion

        #region Section View Configuration

        /// <summary>
        /// Configure section view properties and annotations
        /// </summary>
        public bool ConfigureSectionView(ViewSection sectionView, CableTraySegmentModel traySegment, SectionViewConfiguration config)
        {
            try
            {
                if (sectionView == null || traySegment == null) return false;

                using (var transaction = new Transaction(_document, "Configure Section View"))
                {
                    transaction.Start();

                    // Set view name
                    if (!string.IsNullOrEmpty(config.ViewName))
                    {
                        try
                        {
                            sectionView.Name = config.ViewName;
                        }
                        catch
                        {
                            // Name might not be unique
                        }
                    }

                    // Set view template if specified
                    if (config.ViewTemplateId != null && config.ViewTemplateId != ElementId.InvalidElementId)
                    {
                        sectionView.ViewTemplateId = config.ViewTemplateId;
                    }

                    // Set scale
                    if (config.Scale > 0)
                    {
                        sectionView.Scale = config.Scale;
                    }

                    // Set crop box if specified
                    if (config.CropBoxVisible.HasValue)
                    {
                        sectionView.CropBoxVisible = config.CropBoxVisible.Value;
                    }

                    // Add annotations if requested
                    if (config.AddDimensions)
                    {
                        AddSectionDimensions(sectionView, traySegment);
                    }

                    if (config.AddLabels)
                    {
                        AddSectionLabels(sectionView, traySegment);
                    }

                    transaction.Commit();
                    _logger?.Log($"Configured section view for tray {traySegment.TrayRef}", LogType.Information);
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error configuring section view: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Update section view to show latest cable information
        /// </summary>
        public bool UpdateSectionViewContent(ViewSection sectionView, CableTraySegmentModel traySegment)
        {
            try
            {
                if (sectionView == null || traySegment == null) return false;

                using (var transaction = new Transaction(_document, "Update Section View Content"))
                {
                    transaction.Start();

                    // Regenerate view to show latest model changes
                    _document.Regenerate();

                    // Update any text notes or labels with current cable information
                    UpdateSectionLabels(sectionView, traySegment);

                    transaction.Commit();
                    _logger?.Log($"Updated section view content for tray {traySegment.TrayRef}", LogType.Information);
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error updating section view content: {ex.Message}", LogType.Error);
                return false;
            }
        }

        #endregion

        #region Section View Management

        /// <summary>
        /// Find existing section views for a tray segment
        /// </summary>
        public List<ViewSection> FindExistingSectionViews(CableTraySegmentModel traySegment)
        {
            var existingViews = new List<ViewSection>();

            try
            {
                if (traySegment == null) return existingViews;

                // Check if tray segment already has a section view reference
                if (traySegment.SectionView is ViewSection directView)
                {
                    existingViews.Add(directView);
                }

                // Search for section views that might reference this tray
                var collector = new FilteredElementCollector(_document)
                    .OfClass(typeof(ViewSection));

                foreach (ViewSection view in collector)
                {
                    try
                    {
                        // Check if view name contains tray reference
                        if (view.Name.Contains(traySegment.TrayRef))
                        {
                            if (!existingViews.Contains(view))
                            {
                                existingViews.Add(view);
                            }
                        }
                    }
                    catch
                    {
                        // Skip views that can't be processed
                    }
                }

                _logger?.Log($"Found {existingViews.Count} existing section views for tray {traySegment.TrayRef}", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error finding existing section views: {ex.Message}", LogType.Error);
            }

            return existingViews;
        }

        /// <summary>
        /// Delete a section view
        /// </summary>
        public bool DeleteSectionView(ViewSection sectionView)
        {
            try
            {
                if (sectionView == null || !sectionView.IsValidObject) return false;

                using (var transaction = new Transaction(_document, "Delete Section View"))
                {
                    transaction.Start();

                    _document.Delete(sectionView.Id);

                    transaction.Commit();
                    _logger?.Log($"Deleted section view {sectionView.Name}", LogType.Information);
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error deleting section view: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Get all section views in the document
        /// </summary>
        public List<ViewSection> GetAllSectionViews()
        {
            var sectionViews = new List<ViewSection>();

            try
            {
                var collector = new FilteredElementCollector(_document)
                    .OfClass(typeof(ViewSection));

                sectionViews.AddRange(collector.Cast<ViewSection>());

                _logger?.Log($"Found {sectionViews.Count} section views in document", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error getting all section views: {ex.Message}", LogType.Error);
            }

            return sectionViews;
        }

        #endregion

        #region Validation

        /// <summary>
        /// Validate section view creation requirements
        /// </summary>
        public List<string> ValidateSectionViewCreation(CableTraySegmentModel traySegment)
        {
            var issues = new List<string>();

            try
            {
                if (traySegment == null)
                {
                    issues.Add("Tray segment is null");
                    return issues;
                }

                if (traySegment.CableTray == null || !traySegment.CableTray.IsValidObject)
                {
                    issues.Add("Cable tray element is invalid");
                }

                var centerline = GetTrayCenterline(traySegment.CableTray);
                if (centerline == null)
                {
                    issues.Add("Could not determine tray centerline");
                }

                var sectionViewType = GetSectionViewType();
                if (sectionViewType == null)
                {
                    issues.Add("No section view type available");
                }

                if (string.IsNullOrEmpty(traySegment.TrayRef))
                {
                    issues.Add("Tray reference is empty");
                }
            }
            catch (Exception ex)
            {
                issues.Add($"Validation error: {ex.Message}");
            }

            return issues;
        }

        /// <summary>
        /// Check if section view creation is supported for the tray type
        /// </summary>
        public bool IsSectionViewSupportedForTray(CableTray cableTray)
        {
            try
            {
                if (cableTray == null) return false;

                // Check if tray has valid geometry for section creation
                var centerline = GetTrayCenterline(cableTray);
                return centerline != null;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// Get the centerline curve of a cable tray
        /// </summary>
        private Curve GetTrayCenterline(CableTray cableTray)
        {
            try
            {
                if (cableTray?.Location is LocationCurve locationCurve)
                {
                    return locationCurve.Curve;
                }

                // Alternative: try to extract from geometry
                var options = new Options();
                var geometry = cableTray.get_Geometry(options);
                
                if (geometry != null)
                {
                    foreach (GeometryObject geomObj in geometry)
                    {
                        if (geomObj is Curve curve)
                        {
                            return curve;
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error getting tray centerline: {ex.Message}", LogType.Error);
                return null;
            }
        }

        /// <summary>
        /// Calculate section box for a tray segment
        /// </summary>
        private BoundingBoxXYZ CalculateSectionBox(CableTraySegmentModel traySegment, Curve centerline)
        {
            try
            {
                // Get midpoint of centerline for section location
                var midPoint = centerline.Evaluate(0.5, true);
                
                // Get direction perpendicular to centerline
                var tangent = centerline.ComputeDerivatives(0.5, true).BasisX.Normalize();
                var normal = new XYZ(-tangent.Y, tangent.X, 0).Normalize();
                
                // Create transform for section box
                var transform = CreateSectionTransform(midPoint, normal);
                
                // Calculate section dimensions based on tray size
                var width = Math.Max(traySegment.Width * 2, 5.0); // At least 5 feet wide
                var height = Math.Max(traySegment.Height * 2, 3.0); // At least 3 feet high
                
                return CreateSectionBoundingBox(transform, width, height);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error calculating section box: {ex.Message}", LogType.Error);
                return null;
            }
        }

        /// <summary>
        /// Create transform for section box
        /// </summary>
        private Transform CreateSectionTransform(XYZ origin, XYZ direction)
        {
            var transform = Transform.Identity;
            transform.Origin = origin;
            transform.BasisX = direction.Normalize();
            transform.BasisY = XYZ.BasisZ;
            transform.BasisZ = transform.BasisX.CrossProduct(transform.BasisY);
            return transform;
        }

        /// <summary>
        /// Create bounding box for section
        /// </summary>
        private BoundingBoxXYZ CreateSectionBoundingBox(Transform transform, double width, double height)
        {
            var boundingBox = new BoundingBoxXYZ();
            boundingBox.Transform = transform;
            
            var halfWidth = width / 2.0;
            var halfHeight = height / 2.0;
            var depth = 1.0; // 1 foot depth
            
            boundingBox.Min = new XYZ(-halfWidth, -halfHeight, -depth);
            boundingBox.Max = new XYZ(halfWidth, halfHeight, depth);
            
            return boundingBox;
        }

        /// <summary>
        /// Get section view type
        /// </summary>
        private ViewFamilyType GetSectionViewType()
        {
            try
            {
                var collector = new FilteredElementCollector(_document)
                    .OfClass(typeof(ViewFamilyType));

                foreach (ViewFamilyType viewType in collector)
                {
                    if (viewType.ViewFamily == ViewFamily.Section)
                    {
                        return viewType;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error getting section view type: {ex.Message}", LogType.Error);
                return null;
            }
        }

        /// <summary>
        /// Configure basic section view properties
        /// </summary>
        private void ConfigureSectionView(ViewSection sectionView, CableTraySegmentModel traySegment, string sectionName)
        {
            try
            {
                if (!string.IsNullOrEmpty(sectionName))
                {
                    try
                    {
                        // Hide most categories except cable trays and conduits
                        var categoriesToShow = new[]
                        {
                            BuiltInCategory.OST_CableTray,
                            BuiltInCategory.OST_CableTrayFitting,
                            BuiltInCategory.OST_ElectricalEquipment,
                            BuiltInCategory.OST_GenericModel
                        };

                        var allCategories = _document.Settings.Categories;

                        foreach (Category category in allCategories)
                        {
                            if (category.get_AllowsVisibilityControl(sectionView))
                            {
                                var shouldShow = categoriesToShow.Contains((BuiltInCategory)category.Id.IntegerValue());
                                category.set_Visible(sectionView, shouldShow);
                            }
                        }

                        sectionView.Name = sectionName;
                        sectionView.DetailLevel = ViewDetailLevel.Fine;
                        sectionView.get_Parameter(BuiltInParameter.MODEL_GRAPHICS_STYLE).Set((int)DisplayStyle.Wireframe);
                        sectionView.get_Parameter(BuiltInParameter.VIEW_SCALE).Set(1);
                    }
                    catch
                    {
                        // Name might not be unique
                    }
                }

                // Set reasonable scale
                sectionView.Scale = 48; // 1/4" = 1'-0"
                
                // Show crop box
                sectionView.CropBoxVisible = true;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error configuring section view: {ex.Message}", LogType.Warning);
            }
        }

        /// <summary>
        /// Generate section view name
        /// </summary>
        private string GenerateSectionViewName(CableTraySegmentModel traySegment, SectionViewOptions options)
        {
            var baseName = $"Section - {traySegment.TrayRef}";
            
            if (!string.IsNullOrEmpty(options.NamePrefix))
            {
                baseName = $"{options.NamePrefix} - {traySegment.TrayRef}";
            }
            
            if (!string.IsNullOrEmpty(options.NameSuffix))
            {
                baseName = $"{baseName} - {options.NameSuffix}";
            }
            
            return baseName;
        }

        /// <summary>
        /// Add dimensions to section view
        /// </summary>
        private void AddSectionDimensions(ViewSection sectionView, CableTraySegmentModel traySegment)
        {
            try
            {
                // This would add dimensions showing tray width and height
                // Implementation would depend on specific requirements
                _logger?.Log($"Added dimensions to section view for tray {traySegment.TrayRef}", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error adding section dimensions: {ex.Message}", LogType.Warning);
            }
        }

        /// <summary>
        /// Add labels to section view
        /// </summary>
        private void AddSectionLabels(ViewSection sectionView, CableTraySegmentModel traySegment)
        {
            try
            {
                // This would add text notes showing cable information
                // Implementation would depend on specific requirements
                _logger?.Log($"Added labels to section view for tray {traySegment.TrayRef}", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error adding section labels: {ex.Message}", LogType.Warning);
            }
        }

        /// <summary>
        /// Update labels in section view
        /// </summary>
        private void UpdateSectionLabels(ViewSection sectionView, CableTraySegmentModel traySegment)
        {
            try
            {
                // This would update existing text notes with current cable information
                // Implementation would depend on specific requirements
                _logger?.Log($"Updated labels in section view for tray {traySegment.TrayRef}", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error updating section labels: {ex.Message}", LogType.Warning);
            }
        }

        #endregion
    }
}
