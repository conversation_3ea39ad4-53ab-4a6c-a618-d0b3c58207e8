﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.TraylorSwift.Handlers
{
    public enum RequestId : int
    {
        None = 0,
        AddTraySegment = 1, // Select one tray, find nearby cables, set tray parameter, create tag, add ParametricCable element on tray
        RemoveTraySegment = 2, // Remove tag from selected tray, set tray parameter as empty, delete parametric element on tray
        CreateTraySection = 3, // Create section view of the selected tray
        UpdateParametricCable = 4, // Update ParametricCable element shown in the section view
        ShowInRevit = 5, // Create 3D view or use existing, draw cable run in the view, set section box, set GraphicVisility settings
        WriteTSCablesParameter = 6, // Write TS_Cables parameter to tray element to ensure persistence
    }

    /// <summary>
    /// Thread-safe request configuration class 
    /// Manages request queuing between WPF UI and Revit API via ExternalEvent
    /// </summary>
    public class RequestConfigure
    {
        #region Fields

        // Storing the value as a plain Int makes using the interlocking mechanism simpler
        private int _request = (int)RequestId.None;

        #endregion

        #region Public Methods

        /// <summary>
        /// Make a request for the given request ID
        /// </summary>
        /// <param name="request">The request to make</param>
        public void Make(RequestId request)
        {
            Interlocked.Exchange(ref _request, (int)request);
        }

        /// <summary>
        /// Take the current request and reset to None
        /// This is called by the RequestHandler to process the request
        /// </summary>
        /// <returns>The current request ID</returns>
        public RequestId Take()
        {
            return (RequestId)Interlocked.Exchange(ref _request, (int)RequestId.None);
        }

        /// <summary>
        /// Check if there is a pending request without taking it
        /// </summary>
        /// <returns>True if there is a pending request</returns>
        public bool HasPendingRequest()
        {
            return _request != (int)RequestId.None;
        }

        /// <summary>
        /// Get the current request without taking it
        /// </summary>
        /// <returns>The current request ID</returns>
        public RequestId Peek()
        {
            return (RequestId)_request;
        }

        #endregion
    }

    /// <summary>
    /// Data container for requests that need additional context
    /// Used for complex operations that require specific data
    /// </summary>
    public class PendingData_TS
    {
        public RequestId RequestId { get; set; }
        public object Data { get; set; }
        public string Context { get; set; }
        public DateTime Timestamp { get; set; }

        public PendingData_TS(RequestId requestId)
        {
            RequestId = requestId;
            Timestamp = DateTime.Now;
        }

        public PendingData_TS(RequestId requestId, object data, string context = null)
        {
            RequestId = requestId;
            Data = data;
            Context = context;
            Timestamp = DateTime.Now;
        }
    }
}
