﻿using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using MEP.TraylorSwift.Handlers;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.TraylorSwift.ViewModels
{
    public partial class MainViewModel : BaseViewModel
    {
        #region Constructor

        /// <summary>
        /// Initialize the MainViewModel
        /// </summary>
        /// <param name="serviceProvider">Service provider for dependency injection</param>
        public MainViewModel(IServiceProvider serviceProvider) : base(serviceProvider)
        {
            _logger?.Log("MainViewModel initialized", LogType.Information);
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Initialize the ViewModel with data from TrayloSwift core logic
        /// </summary>
        /// <param name="distributionBoards">Original distribution board data</param>
        /// <param name="projectInfo">Original project information</param>
        /// <param name="requestHandler">Request handler for ExternalEvent</param>
        /// <param name="externalEvent">ExternalEvent for Revit API safety</param>
        public void InitializeWithData(RequestHandler requestHandler, ExternalEvent externalEvent)
        {
            try
            {
                // Call base initialization
                Initialize(requestHandler, externalEvent);

                StatusMessage = "Traylo Swift loaded successfully";
                _logger?.Log($"MainViewModel initialized", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log("Failed to initialize MainViewModel with data", LogType.Error);
                StatusMessage = $"Error loading data: {ex.Message}";
            }
        }

        #endregion

        #region Override Methods

        /// <summary>
        /// Override WakeUp to handle specific main window wake up logic
        /// </summary>
        public override void WakeUp()
        {
            base.WakeUp();

            try
            {
                StatusMessage = "Operation completed";
                _logger?.Log("MainViewModel woken up and refreshed", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log("Failed to wake up MainViewModel", LogType.Error);
                StatusMessage = $"Error refreshing data: {ex.Message}";
            }
        }

        #endregion
    }
}
