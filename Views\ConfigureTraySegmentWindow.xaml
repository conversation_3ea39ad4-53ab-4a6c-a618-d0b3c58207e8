<Window
    x:Class="MEP.TraylorSwift.Views.ConfigureTraySegmentWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="Configure Tray Segment"
    Width="800"
    Height="600"
    MinWidth="600"
    MinHeight="400"
    WindowStartupLocation="CenterOwner"
    mc:Ignorable="d">

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0"
                   Margin="0,0,0,10"
                   Text="{Binding TraySegment.TrayRef, StringFormat='Configure Tray Segment: {0}'}" />

        <!-- Cables DataGrid -->
        <materialDesign:Card Grid.Row="1" Padding="10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0"
                          Margin="0,0,0,10"
                          Text="Cables in Tray" />

                <DataGrid Grid.Row="1"
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         ItemsSource="{Binding TraySegment.Cables}"
                         SelectionMode="Single">
                    <DataGrid.Columns>
                        <DataGridTextColumn Binding="{Binding CableRef}"
                                           Header="Cable Ref."
                                           Width="120" />
                        <DataGridTextColumn Binding="{Binding Name}"
                                           Header="Name"
                                           Width="150" />
                        <DataGridTextColumn Binding="{Binding GetToEquipmentName}"
                                           Header="To"
                                           Width="120" />
                        <DataGridTextColumn Binding="{Binding GetFromEquipmentName}"
                                           Header="From"
                                           Width="120" />
                        <DataGridTextColumn Binding="{Binding Diameter, StringFormat=F2}"
                                           Header="Diameter"
                                           Width="80" />
                        <DataGridTextColumn Binding="{Binding Weight, StringFormat=F2}"
                                           Header="Weight"
                                           Width="80" />
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>

        <!-- Section View Controls -->
        <materialDesign:Card Grid.Row="2" Margin="0,10,0,0" Padding="10">
            <StackPanel>
                <TextBlock Text="Section View" />

                <TextBox Margin="0,5,0,0"
                        materialDesign:HintAssist.Hint="Section View Name"
                        IsReadOnly="{Binding HasSectionView}"
                        Text="{Binding SectionViewName, UpdateSourceTrigger=PropertyChanged}" />

                <Button Margin="0,10,0,0"
                       Command="{Binding CreateSectionCommand}"
                       Content="Create Section"
                       IsEnabled="{Binding CanCreateSection}" />
            </StackPanel>
        </materialDesign:Card>

        <!-- Parametric Cable Controls -->
        <Border Grid.Row="3"
               Margin="0,10,0,0"
               Padding="10"
               CornerRadius="4"
               IsEnabled="{Binding HasSectionView}">
            <StackPanel>
                <TextBlock Text="Parametric Cable Parameters" />

                <Grid Margin="0,10,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="10" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="10" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <TextBox Grid.Column="0"
                            materialDesign:HintAssist.Hint="Minimum Space Capacity (%)"
                            Text="{Binding MinimumSpaceCapacity, UpdateSourceTrigger=PropertyChanged}" />

                    <ComboBox Grid.Column="2"
                             materialDesign:HintAssist.Hint="Cable Arrangement"
                             SelectedItem="{Binding CableArrangement}">
                        <ComboBoxItem Content="trefoil" />
                        <ComboBoxItem Content="flat" />
                    </ComboBox>

                    <ComboBox Grid.Column="4"
                             materialDesign:HintAssist.Hint="Cable Spacing"
                             SelectedItem="{Binding CableSpacing}">
                        <ComboBoxItem Content="Touching" />
                        <ComboBoxItem Content="D" />
                        <ComboBoxItem Content="2D" />
                    </ComboBox>
                </Grid>

                <Button Margin="0,10,0,0"
                       Command="{Binding UpdateParametersCommand}"
                       Content="Update Parameters" />
            </StackPanel>
        </Border>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="4"
                   Margin="0,10,0,0"
                   HorizontalAlignment="Right"
                   Orientation="Horizontal">
            <Button Margin="0,0,10,0"
                   Command="{Binding CloseCommand}"
                   Content="Close" />
        </StackPanel>
    </Grid>
</Window>
