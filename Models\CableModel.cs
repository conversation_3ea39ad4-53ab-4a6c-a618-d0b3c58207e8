using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;

namespace MEP.TraylorSwift.Models
{
    /// <summary>
    /// Represents an individual cable with its properties and routing information
    /// </summary>
    public class CableModel
    {
        #region Properties

        /// <summary>
        /// Reference to the associated cable tray segment containing this cable
        /// </summary>
        public CableTraySegmentModel AssociatedTraySegment { get; set; }

        /// <summary>
        /// Unique cable reference identifier
        /// </summary>
        public string CableRef { get; set; }

        /// <summary>
        /// Display name of the cable
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Destination electrical equipment element
        /// </summary>
        public Element To { get; set; }

        /// <summary>
        /// Source electrical equipment element
        /// </summary>
        public Element From { get; set; }

        /// <summary>
        /// Cable diameter in project units
        /// </summary>
        public double Diameter { get; set; }

        /// <summary>
        /// Cable weight in project units
        /// </summary>
        public double Weight { get; set; }

        /// <summary>
        /// Circuit path geometry points for cable routing
        /// </summary>
        public List<XYZ> RoutePoints { get; set; }

        /// <summary>
        /// Cable type or category for classification
        /// </summary>
        public string CableType { get; set; }

        /// <summary>
        /// Voltage rating of the cable
        /// </summary>
        public double VoltageRating { get; set; }

        /// <summary>
        /// Current carrying capacity in amperes
        /// </summary>
        public double CurrentCapacity { get; set; }

        /// <summary>
        /// Number of conductors in the cable
        /// </summary>
        public int NumberOfConductors { get; set; }

        /// <summary>
        /// Cross-sectional area of the cable
        /// </summary>
        public double CrossSectionalArea { get; set; }

        /// <summary>
        /// Indicates if this cable is currently selected or highlighted
        /// </summary>
        public bool IsSelected { get; set; }

        /// <summary>
        /// Additional properties for extensibility
        /// </summary>
        public Dictionary<string, object> AdditionalProperties { get; set; }

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize a new cable model
        /// </summary>
        public CableModel()
        {
            RoutePoints = new List<XYZ>();
            AdditionalProperties = new Dictionary<string, object>();
            CableRef = string.Empty;
            Name = string.Empty;
            CableType = string.Empty;
        }

        /// <summary>
        /// Initialize a new cable model with basic properties
        /// </summary>
        /// <param name="cableRef">Cable reference identifier</param>
        /// <param name="name">Cable display name</param>
        public CableModel(string cableRef, string name) : this()
        {
            CableRef = cableRef ?? string.Empty;
            Name = name ?? string.Empty;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Get the display name for the "To" equipment
        /// </summary>
        /// <returns>Equipment name or empty string if not available</returns>
        public string GetToEquipmentName()
        {
            try
            {
                return To?.Name ?? string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Get the display name for the "From" equipment
        /// </summary>
        /// <returns>Equipment name or empty string if not available</returns>
        public string GetFromEquipmentName()
        {
            try
            {
                return From?.Name ?? string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Calculate the total length of the cable route
        /// </summary>
        /// <returns>Total route length in project units</returns>
        public double CalculateRouteLength()
        {
            if (RoutePoints == null || RoutePoints.Count < 2)
                return 0.0;

            double totalLength = 0.0;
            for (int i = 1; i < RoutePoints.Count; i++)
            {
                totalLength += RoutePoints[i - 1].DistanceTo(RoutePoints[i]);
            }

            return totalLength;
        }

        /// <summary>
        /// Get the bounding box of the cable route
        /// </summary>
        /// <returns>Bounding box or null if no route points</returns>
        public BoundingBoxXYZ GetRouteBoundingBox()
        {
            if (RoutePoints == null || RoutePoints.Count == 0)
                return null;

            var min = new XYZ(double.MaxValue, double.MaxValue, double.MaxValue);
            var max = new XYZ(double.MinValue, double.MinValue, double.MinValue);

            foreach (var point in RoutePoints)
            {
                min = new XYZ(
                    Math.Min(min.X, point.X),
                    Math.Min(min.Y, point.Y),
                    Math.Min(min.Z, point.Z)
                );
                max = new XYZ(
                    Math.Max(max.X, point.X),
                    Math.Max(max.Y, point.Y),
                    Math.Max(max.Z, point.Z)
                );
            }

            var bbox = new BoundingBoxXYZ();
            bbox.Min = min;
            bbox.Max = max;
            return bbox;
        }

        /// <summary>
        /// Check if this cable intersects with a given bounding box
        /// </summary>
        /// <param name="boundingBox">Bounding box to check intersection with</param>
        /// <param name="tolerance">Tolerance for intersection check</param>
        /// <returns>True if cable route intersects the bounding box</returns>
        public bool IntersectsWith(BoundingBoxXYZ boundingBox, double tolerance = 0.1)
        {
            if (RoutePoints == null || RoutePoints.Count < 2 || boundingBox == null)
                return false;

            // Don't expand bounding box here - let the caller handle tolerance
            // This prevents double tolerance application
            var boxMin = boundingBox.Min;
            var boxMax = boundingBox.Max;

            // Check each line segment of the route
            for (int i = 1; i < RoutePoints.Count; i++)
            {
                var start = RoutePoints[i - 1];
                var end = RoutePoints[i];

                if (LineSegmentIntersectsBoundingBox(start, end, boxMin, boxMax))
                    return true;
            }

            return false;
        }

        /// <summary>
        /// Create a copy of this cable model
        /// </summary>
        /// <returns>New cable model with copied properties</returns>
        public CableModel Clone()
        {
            var clone = new CableModel(CableRef, Name)
            {
                To = To,
                From = From,
                Diameter = Diameter,
                Weight = Weight,
                CableType = CableType,
                VoltageRating = VoltageRating,
                CurrentCapacity = CurrentCapacity,
                NumberOfConductors = NumberOfConductors,
                CrossSectionalArea = CrossSectionalArea,
                IsSelected = IsSelected
            };

            // Copy route points
            if (RoutePoints != null)
            {
                clone.RoutePoints = new List<XYZ>(RoutePoints);
            }

            // Copy additional properties
            if (AdditionalProperties != null)
            {
                clone.AdditionalProperties = new Dictionary<string, object>(AdditionalProperties);
            }

            return clone;
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Check if a line segment intersects with a bounding box
        /// </summary>
        /// <param name="start">Start point of line segment</param>
        /// <param name="end">End point of line segment</param>
        /// <param name="boxMin">Minimum point of bounding box</param>
        /// <param name="boxMax">Maximum point of bounding box</param>
        /// <returns>True if line intersects bounding box</returns>
        private bool LineIntersectsBoundingBox(XYZ start, XYZ end, XYZ boxMin, XYZ boxMax)
        {
            // Simple implementation - check if either endpoint is inside box
            // or if line segment crosses any face of the box
            return IsPointInBoundingBox(start, boxMin, boxMax) ||
                   IsPointInBoundingBox(end, boxMin, boxMax) ||
                   LineSegmentIntersectsBoundingBox(start, end, boxMin, boxMax);
        }

        /// <summary>
        /// Check if a point is inside a bounding box
        /// </summary>
        private bool IsPointInBoundingBox(XYZ point, XYZ boxMin, XYZ boxMax)
        {
            return point.X >= boxMin.X && point.X <= boxMax.X &&
                   point.Y >= boxMin.Y && point.Y <= boxMax.Y &&
                   point.Z >= boxMin.Z && point.Z <= boxMax.Z;
        }

        /// <summary>
        /// Robust line segment to bounding box intersection test using separating axis theorem
        /// </summary>
        private bool LineSegmentIntersectsBoundingBox(XYZ start, XYZ end, XYZ boxMin, XYZ boxMax)
        {
            try
            {
                // Check if either endpoint is inside the box
                if (IsPointInBoundingBox(start, boxMin, boxMax) || IsPointInBoundingBox(end, boxMin, boxMax))
                    return true;

                // Check if line segment intersects any face of the bounding box
                var direction = end - start;
                var length = direction.GetLength();

                if (length < 1e-6) return false; // Degenerate line segment

                direction = direction.Normalize();

                // Use parametric line equation: P(t) = start + t * direction, where 0 <= t <= length
                double tMin = 0.0;
                double tMax = length;

                // Check intersection with each axis-aligned slab
                for (int axis = 0; axis < 3; axis++)
                {
                    double startCoord = GetCoordinate(start, axis);
                    double dirCoord = GetCoordinate(direction, axis);
                    double minCoord = GetCoordinate(boxMin, axis);
                    double maxCoord = GetCoordinate(boxMax, axis);

                    if (Math.Abs(dirCoord) < 1e-9) // Line is parallel to this axis
                    {
                        // If line is outside the slab, no intersection
                        if (startCoord < minCoord || startCoord > maxCoord)
                            return false;
                    }
                    else
                    {
                        // Calculate intersection parameters
                        double t1 = (minCoord - startCoord) / dirCoord;
                        double t2 = (maxCoord - startCoord) / dirCoord;

                        // Ensure t1 <= t2
                        if (t1 > t2) { double temp = t1; t1 = t2; t2 = temp; }

                        // Update the intersection interval
                        tMin = Math.Max(tMin, t1);
                        tMax = Math.Min(tMax, t2);

                        // If tMin > tMax, no intersection
                        if (tMin > tMax)
                            return false;
                    }
                }

                // If we get here, there is an intersection
                return tMin <= tMax;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Get coordinate value by axis index (0=X, 1=Y, 2=Z)
        /// </summary>
        private double GetCoordinate(XYZ point, int axis)
        {
            switch (axis)
            {
                case 0: return point.X;
                case 1: return point.Y;
                case 2: return point.Z;
                default: return 0.0;
            }
        }

        #endregion

        #region Overrides

        /// <summary>
        /// String representation of the cable
        /// </summary>
        public override string ToString()
        {
            return $"Cable: {CableRef} - {Name} ({GetFromEquipmentName()} → {GetToEquipmentName()})";
        }

        /// <summary>
        /// Equality comparison based on cable reference
        /// </summary>
        public override bool Equals(object obj)
        {
            if (obj is CableModel other)
            {
                return string.Equals(CableRef, other.CableRef, StringComparison.OrdinalIgnoreCase);
            }
            return false;
        }

        /// <summary>
        /// Hash code based on cable reference
        /// </summary>
        public override int GetHashCode()
        {
            return CableRef?.GetHashCode() ?? 0;
        }

        #endregion
    }
}
