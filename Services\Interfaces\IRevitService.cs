﻿using Autodesk.Revit.UI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.TraylorSwift.Services.Interfaces
{
    internal interface IRevitService
    {
        #region Transaction Management

        /// <param name="transactionName">Name of the transaction</param>
        /// <param name="operation">Operation to execute</param>
        /// <returns>True if successful</returns>
        bool ExecuteInTransaction(string transactionName, Func<bool> operation);

        #endregion

        #region Document Properties

        /// <summary>
        /// Get the current Revit document
        /// </summary>
        Document Document { get; }

        /// <summary>
        /// Get the current UI document
        /// </summary>
        UIDocument UIDocument { get; }

        /// <summary>
        /// Get the current UI application
        /// </summary>
        UIApplication UIApplication { get; }

        #endregion
    }
}
