using Autodesk.Revit.DB;
using MEP.TraylorSwift.Models;
using System.Collections.Generic;

namespace MEP.TraylorSwift.Services.Interfaces
{
    /// <summary>
    /// Service for loading and initializing TS_Data from Revit document
    /// </summary>
    public interface IDataLoadingService
    {
        #region Data Loading

        /// <summary>
        /// Load existing cable tray segments with TS_Cables parameter data
        /// </summary>
        /// <returns>List of cable tray segment models</returns>
        List<CableTraySegmentModel> LoadExistingTraySegments();

        /// <summary>
        /// Load all cables from electrical circuits
        /// </summary>
        /// <returns>Dictionary of cables indexed by cable reference</returns>
        Dictionary<string, CableModel> LoadAllCablesFromCircuits();

        /// <summary>
        /// Load electrical equipment from the document
        /// </summary>
        /// <returns>Dictionary of electrical equipment indexed by element ID</returns>
        Dictionary<ElementId, Element> LoadElectricalEquipment();

        /// <summary>
        /// Load levels from the document
        /// </summary>
        /// <returns>Dictionary of levels indexed by element ID</returns>
        Dictionary<ElementId, Level> LoadLevels();

        #endregion

        #region Data Initialization

        /// <summary>
        /// Initialize spatial indexes for efficient queries
        /// </summary>
        /// <param name="tsData">TS_Data to initialize spatial indexes for</param>
        /// <returns>True if spatial indexes were initialized successfully</returns>
        bool InitializeSpatialIndexes(TS_Data tsData);

        /// <summary>
        /// Associate cables with tray segments based on existing TS_Cables parameters
        /// </summary>
        /// <param name="traySegments">Tray segments to associate cables with</param>
        /// <param name="availableCables">Available cables to associate</param>
        /// <returns>Number of cable associations created</returns>
        int AssociateCablesWithTraySegments(List<CableTraySegmentModel> traySegments, Dictionary<string, CableModel> availableCables);

        /// <summary>
        /// Load and associate parametric cables with tray segments
        /// </summary>
        /// <param name="traySegments">Tray segments to load parametric cables for</param>
        /// <returns>Number of parametric cables loaded</returns>
        int LoadParametricCables(List<CableTraySegmentModel> traySegments);

        /// <summary>
        /// Load and associate section views with tray segments
        /// </summary>
        /// <param name="traySegments">Tray segments to load section views for</param>
        /// <returns>Number of section views loaded</returns>
        int LoadSectionViews(List<CableTraySegmentModel> traySegments);

        #endregion

        #region Data Validation

        /// <summary>
        /// Validate loaded TS_Data for consistency and integrity
        /// </summary>
        /// <param name="tsData">TS_Data to validate</param>
        /// <returns>List of validation issues found</returns>
        List<string> ValidateLoadedData(TS_Data tsData);

        /// <summary>
        /// Clean up orphaned references in loaded data
        /// </summary>
        /// <param name="tsData">TS_Data to clean up</param>
        /// <returns>Number of orphaned references cleaned up</returns>
        int CleanupOrphanedReferences(TS_Data tsData);

        /// <summary>
        /// Verify that all referenced elements exist and are valid
        /// </summary>
        /// <param name="tsData">TS_Data to verify</param>
        /// <returns>List of invalid references found</returns>
        List<string> VerifyElementReferences(TS_Data tsData);

        #endregion

        #region Data Refresh

        /// <summary>
        /// Update cable data from latest circuit information
        /// </summary>
        /// <param name="tsData">TS_Data to update cable data for</param>
        /// <returns>Number of cables updated</returns>
        int UpdateCableDataFromCircuits(TS_Data tsData);

        #endregion

        #region Performance Optimization

        /// <summary>
        /// Load data with progress reporting
        /// </summary>
        /// <param name="progressCallback">Callback for progress updates</param>
        /// <returns>Loaded TS_Data</returns>
        TS_Data LoadTSDataWithProgress(System.Action<string, double> progressCallback);

        /// <summary>
        /// Load data incrementally for large documents
        /// </summary>
        /// <param name="batchSize">Number of elements to process per batch</param>
        /// <returns>Loaded TS_Data</returns>
        TS_Data LoadTSDataIncrementally(int batchSize = 100);

        /// <summary>
        /// Check if document has changed since last load
        /// </summary>
        /// <param name="lastLoadTime">Time of last data load</param>
        /// <returns>True if document has changed</returns>
        bool HasDocumentChanged(System.DateTime lastLoadTime);

        #endregion

    }


    /// <summary>
    /// Options for data loading operation
    /// </summary>
    public class DataLoadingOptions
    {
        public bool LoadCableTrays { get; set; } = true;
        public bool LoadCircuits { get; set; } = true;
        public bool LoadElectricalEquipment { get; set; } = true;
        public bool LoadParametricCables { get; set; } = true;
        public bool LoadSectionViews { get; set; } = true;
        public bool LoadTags { get; set; } = true;
        public bool InitializeSpatialIndexes { get; set; } = true;
        public bool ValidateData { get; set; } = true;
        public bool CleanupOrphanedReferences { get; set; } = true;
        public int BatchSize { get; set; } = 100;
        public double GeometricTolerance { get; set; } = 0.1;
        public bool ReportProgress { get; set; } = false;

        public static DataLoadingOptions Default => new DataLoadingOptions();
        
        public static DataLoadingOptions Fast => new DataLoadingOptions
        {
            LoadParametricCables = false,
            LoadSectionViews = false,
            LoadTags = false,
            InitializeSpatialIndexes = false,
            ValidateData = false,
            CleanupOrphanedReferences = false
        };

        public static DataLoadingOptions Complete => new DataLoadingOptions
        {
            LoadCableTrays = true,
            LoadCircuits = true,
            LoadElectricalEquipment = true,
            LoadParametricCables = true,
            LoadSectionViews = true,
            LoadTags = true,
            InitializeSpatialIndexes = true,
            ValidateData = true,
            CleanupOrphanedReferences = true,
            ReportProgress = true
        };
    }
}
