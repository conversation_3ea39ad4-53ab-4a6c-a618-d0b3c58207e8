using System;

namespace MEP.TraylorSwift.Models
{
    /// <summary>
    /// Cable arrangement types for parametric cable visualization
    /// </summary>
    public enum CableArrangement
    {
        /// <summary>
        /// Cables arranged in a triangular trefoil pattern
        /// </summary>
        Trefoil,

        /// <summary>
        /// Cables arranged in a flat horizontal pattern
        /// </summary>
        Flat
    }

    /// <summary>
    /// Cable spacing options for parametric cable visualization
    /// </summary>
    public enum CableSpacing
    {
        /// <summary>
        /// Cables are touching each other
        /// </summary>
        Touching,

        /// <summary>
        /// Cables are spaced by one diameter (D)
        /// </summary>
        D,

        /// <summary>
        /// Cables are spaced by two diameters (2D)
        /// </summary>
        TwoD
    }

    /// <summary>
    /// Types of cable tray segments
    /// </summary>
    public enum TraySegmentType
    {
        /// <summary>
        /// Straight tray segment
        /// </summary>
        Straight,

        /// <summary>
        /// Curved or bent tray segment
        /// </summary>
        Curved,

        /// <summary>
        /// Tray fitting (tee, cross, reducer, etc.)
        /// </summary>
        Fitting,

        /// <summary>
        /// Unknown or unclassified tray type
        /// </summary>
        Unknown
    }

    /// <summary>
    /// Cable types for classification
    /// </summary>
    public enum CableType
    {
        /// <summary>
        /// Power cable
        /// </summary>
        Power,

        /// <summary>
        /// Control cable
        /// </summary>
        Control,

        /// <summary>
        /// Data/communication cable
        /// </summary>
        Data,

        /// <summary>
        /// Instrumentation cable
        /// </summary>
        Instrumentation,

        /// <summary>
        /// Fire alarm cable
        /// </summary>
        FireAlarm,

        /// <summary>
        /// Lighting cable
        /// </summary>
        Lighting,

        /// <summary>
        /// Unknown or unclassified cable type
        /// </summary>
        Unknown
    }

    /// <summary>
    /// Voltage levels for cable classification
    /// </summary>
    public enum VoltageLevel
    {
        /// <summary>
        /// Low voltage (typically up to 1kV)
        /// </summary>
        LowVoltage,

        /// <summary>
        /// Medium voltage (typically 1kV to 35kV)
        /// </summary>
        MediumVoltage,

        /// <summary>
        /// High voltage (typically above 35kV)
        /// </summary>
        HighVoltage,

        /// <summary>
        /// Extra low voltage (typically up to 50V)
        /// </summary>
        ExtraLowVoltage,

        /// <summary>
        /// Unknown voltage level
        /// </summary>
        Unknown
    }

    /// <summary>
    /// Status of tray segment processing
    /// </summary>
    public enum TraySegmentStatus
    {
        /// <summary>
        /// Tray segment is new and not processed
        /// </summary>
        New,

        /// <summary>
        /// Tray segment is being processed
        /// </summary>
        Processing,

        /// <summary>
        /// Tray segment has been successfully processed
        /// </summary>
        Processed,

        /// <summary>
        /// Tray segment processing failed
        /// </summary>
        Failed,

        /// <summary>
        /// Tray segment has been modified and needs reprocessing
        /// </summary>
        Modified
    }

    /// <summary>
    /// Types of geometric analysis for cable detection
    /// </summary>
    public enum GeometricAnalysisType
    {
        /// <summary>
        /// Bounding box intersection analysis
        /// </summary>
        BoundingBox,

        /// <summary>
        /// Precise geometric intersection analysis
        /// </summary>
        Precise,

        /// <summary>
        /// Proximity-based analysis
        /// </summary>
        Proximity,

        /// <summary>
        /// Circuit path analysis
        /// </summary>
        CircuitPath
    }

    /// <summary>
    /// View types for section view creation
    /// </summary>
    public enum SectionViewType
    {
        /// <summary>
        /// Cross-section view perpendicular to tray direction
        /// </summary>
        CrossSection,

        /// <summary>
        /// Longitudinal section view along tray direction
        /// </summary>
        LongitudinalSection,

        /// <summary>
        /// Detail view of tray connection
        /// </summary>
        Detail
    }

    /// <summary>
    /// Spatial indexing types for geometric queries
    /// </summary>
    public enum SpatialIndexType
    {
        /// <summary>
        /// R-Tree spatial index
        /// </summary>
        RTree,

        /// <summary>
        /// Quadtree spatial index
        /// </summary>
        Quadtree,

        /// <summary>
        /// Grid-based spatial index
        /// </summary>
        Grid,

        /// <summary>
        /// Simple list-based index (no optimization)
        /// </summary>
        Simple
    }

    /// <summary>
    /// Operation result status
    /// </summary>
    public enum OperationResult
    {
        /// <summary>
        /// Operation completed successfully
        /// </summary>
        Success,

        /// <summary>
        /// Operation failed
        /// </summary>
        Failed,

        /// <summary>
        /// Operation was cancelled by user
        /// </summary>
        Cancelled,

        /// <summary>
        /// Operation timed out
        /// </summary>
        Timeout,

        /// <summary>
        /// Operation completed with warnings
        /// </summary>
        Warning
    }

    /// <summary>
    /// Log levels for application logging
    /// </summary>
    public enum LogLevel
    {
        /// <summary>
        /// Debug information
        /// </summary>
        Debug,

        /// <summary>
        /// General information
        /// </summary>
        Information,

        /// <summary>
        /// Warning message
        /// </summary>
        Warning,

        /// <summary>
        /// Error message
        /// </summary>
        Error,

        /// <summary>
        /// Critical error
        /// </summary>
        Critical
    }
}

namespace MEP.TraylorSwift.Models.Extensions
{
    /// <summary>
    /// Extension methods for enums
    /// </summary>
    public static class EnumExtensions
    {
        /// <summary>
        /// Get display name for cable arrangement
        /// </summary>
        public static string GetDisplayName(this CableArrangement arrangement)
        {
            return arrangement switch
            {
                CableArrangement.Trefoil => "Trefoil",
                CableArrangement.Flat => "Flat",
                _ => arrangement.ToString()
            };
        }

        /// <summary>
        /// Get display name for cable spacing
        /// </summary>
        public static string GetDisplayName(this CableSpacing spacing)
        {
            return spacing switch
            {
                CableSpacing.Touching => "Touching",
                CableSpacing.D => "D",
                CableSpacing.TwoD => "2D",
                _ => spacing.ToString()
            };
        }

        /// <summary>
        /// Get multiplier value for cable spacing
        /// </summary>
        public static double GetSpacingMultiplier(this CableSpacing spacing)
        {
            return spacing switch
            {
                CableSpacing.Touching => 0.0,
                CableSpacing.D => 1.0,
                CableSpacing.TwoD => 2.0,
                _ => 0.0
            };
        }

        /// <summary>
        /// Get display name for cable type
        /// </summary>
        public static string GetDisplayName(this CableType cableType)
        {
            return cableType switch
            {
                CableType.Power => "Power",
                CableType.Control => "Control",
                CableType.Data => "Data/Communication",
                CableType.Instrumentation => "Instrumentation",
                CableType.FireAlarm => "Fire Alarm",
                CableType.Lighting => "Lighting",
                CableType.Unknown => "Unknown",
                _ => cableType.ToString()
            };
        }

        /// <summary>
        /// Get display name for voltage level
        /// </summary>
        public static string GetDisplayName(this VoltageLevel voltageLevel)
        {
            return voltageLevel switch
            {
                VoltageLevel.ExtraLowVoltage => "Extra Low Voltage (ELV)",
                VoltageLevel.LowVoltage => "Low Voltage (LV)",
                VoltageLevel.MediumVoltage => "Medium Voltage (MV)",
                VoltageLevel.HighVoltage => "High Voltage (HV)",
                VoltageLevel.Unknown => "Unknown",
                _ => voltageLevel.ToString()
            };
        }

        /// <summary>
        /// Parse cable arrangement from string
        /// </summary>
        public static CableArrangement ParseCableArrangement(string value)
        {
            if (string.IsNullOrEmpty(value))
                return CableArrangement.Flat;

            return value.ToLowerInvariant() switch
            {
                "trefoil" => CableArrangement.Trefoil,
                "flat" => CableArrangement.Flat,
                _ => CableArrangement.Flat
            };
        }

        /// <summary>
        /// Parse cable spacing from string
        /// </summary>
        public static CableSpacing ParseCableSpacing(string value)
        {
            if (string.IsNullOrEmpty(value))
                return CableSpacing.Touching;

            return value.ToLowerInvariant() switch
            {
                "touching" => CableSpacing.Touching,
                "d" => CableSpacing.D,
                "2d" => CableSpacing.TwoD,
                _ => CableSpacing.Touching
            };
        }

        /// <summary>
        /// Check if operation result indicates success
        /// </summary>
        public static bool IsSuccess(this OperationResult result)
        {
            return result == OperationResult.Success || result == OperationResult.Warning;
        }

        /// <summary>
        /// Check if operation result indicates failure
        /// </summary>
        public static bool IsFailure(this OperationResult result)
        {
            return result == OperationResult.Failed || result == OperationResult.Timeout;
        }
    }
}
