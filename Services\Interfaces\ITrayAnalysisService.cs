using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using MEP.TraylorSwift.Models;
using System.Collections.Generic;

namespace MEP.TraylorSwift.Services.Interfaces
{
    /// <summary>
    /// Service for analyzing cable trays and their geometric properties
    /// </summary>
    public interface ITrayAnalysisService
    {
        #region Tray Discovery

        /// <summary>
        /// Find all cable trays in the document
        /// </summary>
        /// <returns>List of cable tray family instances</returns>
        List<CableTray> FindAllCableTrays();

        /// <summary>
        /// Find cable trays with existing TS_Cables parameter data
        /// </summary>
        /// <returns>List of cable tray family instances with cable data</returns>
        List<CableTray> FindCableTraysWithCableData();

        /// <summary>
        /// Create cable tray segment model from family instance
        /// </summary>
        /// <param name="cableTray">Cable tray family instance</param>
        /// <returns>Cable tray segment model</returns>
        CableTraySegmentModel CreateTraySegmentModel(CableTray cableTray);

        #endregion

        #region Geometric Analysis

        /// <summary>
        /// Get the bounding box of a cable tray
        /// </summary>
        /// <param name="cableTray">Cable tray family instance</param>
        /// <returns>Bounding box in model coordinates</returns>
        BoundingBoxXYZ GetTrayBoundingBox(CableTray cableTray);

        /// <summary>
        /// Get the centerline curve of a cable tray
        /// </summary>
        /// <param name="cableTray">Cable tray family instance</param>
        /// <returns>Centerline curve or null if not available</returns>
        Curve GetTrayCenterline(CableTray cableTray);

        /// <summary>
        /// Get the cross-sectional area of a cable tray
        /// </summary>
        /// <param name="cableTray">Cable tray family instance</param>
        /// <returns>Cross-sectional area in square units</returns>
        double GetTrayCrossSectionalArea(CableTray cableTray);

        /// <summary>
        /// Check if two cable trays are connected
        /// </summary>
        /// <param name="tray1">First cable tray</param>
        /// <param name="tray2">Second cable tray</param>
        /// <param name="tolerance">Connection tolerance</param>
        /// <returns>True if trays are connected</returns>
        bool AreTraysConnected(CableTray tray1, CableTray tray2, double tolerance = 0.1);

        /// <summary>
        /// Find connected cable trays
        /// </summary>
        /// <param name="cableTray">Source cable tray</param>
        /// <param name="tolerance">Connection tolerance</param>
        /// <returns>List of connected cable trays</returns>
        List<CableTray> FindConnectedTrays(CableTray cableTray, double tolerance = 0.1);

        #endregion

        #region Capacity Analysis

        /// <summary>
        /// Calculate the capacity utilization of a tray segment
        /// </summary>
        /// <param name="traySegment">Tray segment to analyze</param>
        /// <returns>Capacity utilization percentage (0-100)</returns>
        double CalculateCapacityUtilization(CableTraySegmentModel traySegment);

        /// <summary>
        /// Calculate the total weight of a tray segment including cables
        /// </summary>
        /// <param name="traySegment">Tray segment to analyze</param>
        /// <returns>Total weight in project units</returns>
        double CalculateTotalWeight(CableTraySegmentModel traySegment);

        /// <summary>
        /// Check if adding cables would exceed tray capacity
        /// </summary>
        /// <param name="traySegment">Tray segment to check</param>
        /// <param name="additionalCables">Cables to add</param>
        /// <param name="maxCapacityPercent">Maximum allowed capacity percentage</param>
        /// <returns>True if capacity would be exceeded</returns>
        bool WouldExceedCapacity(CableTraySegmentModel traySegment, List<CableModel> additionalCables, double maxCapacityPercent = 80.0);

        #endregion

        #region Parameter Management

        /// <summary>
        /// Read TS_Cables parameter from cable tray
        /// </summary>
        /// <param name="cableTray">Cable tray family instance</param>
        /// <returns>Comma-separated cable references or empty string</returns>
        string ReadTSCablesParameter(CableTray cableTray);

        /// <summary>
        /// Write TS_Cables parameter to cable tray
        /// </summary>
        /// <param name="cableTray">Cable tray family instance</param>
        /// <param name="cableReferences">Comma-separated cable references</param>
        /// <returns>True if parameter was written successfully</returns>
        bool WriteTSCablesParameter(CableTray cableTray, string cableReferences);

        /// <summary>
        /// Read TS_ParametricCable parameter from cable tray
        /// </summary>
        /// <param name="cableTray">Cable tray family instance</param>
        /// <returns>Element ID of parametric cable or null</returns>
        string ReadTSParametricCableParameter(CableTray cableTray);

        /// <summary>
        /// Write TS_ParametricCable parameter to cable tray
        /// </summary>
        /// <param name="cableTray">Cable tray family instance</param>
        /// <param name="parametricCableId">Element ID of parametric cable</param>
        /// <returns>True if parameter was written successfully</returns>
        bool WriteTSParametricCableParameter(CableTray cableTray, ElementId parametricCableId);

        /// <summary>
        /// Read TS_SectionView parameter from cable tray
        /// </summary>
        /// <param name="cableTray">Cable tray family instance</param>
        /// <returns>Element ID of section view or null</returns>
        string ReadTSSectionViewParameter(CableTray cableTray);

        /// <summary>
        /// Write TS_SectionView parameter to cable tray
        /// </summary>
        /// <param name="cableTray">Cable tray family instance</param>
        /// <param name="sectionViewId">Element ID of section view</param>
        /// <returns>True if parameter was written successfully</returns>
        bool WriteTSSectionViewParameter(CableTray cableTray, ElementId sectionViewId);

        /// <summary>
        /// Write TS_Tag parameter to cable tray
        /// </summary>
        /// <param name="cableTray">Cable tray family instance</param>
        /// <param name="sectionViewId">Element ID of section view</param>
        /// <returns>True if parameter was written successfully</returns>
        bool WriteTSTagParameter(CableTray cableTray, ElementId sectionViewId);

        #endregion

        #region Validation

        /// <summary>
        /// Validate cable tray segment data
        /// </summary>
        /// <param name="traySegment">Tray segment to validate</param>
        /// <returns>List of validation issues</returns>
        List<string> ValidateTraySegment(CableTraySegmentModel traySegment);

        /// <summary>
        /// Check if cable tray is valid for processing
        /// </summary>
        /// <param name="cableTray">Cable tray to check</param>
        /// <returns>True if tray is valid</returns>
        bool IsValidCableTray(CableTray cableTray);

        /// <summary>
        /// Get tray segment type based on geometry
        /// </summary>
        /// <param name="cableTray">Cable tray family instance</param>
        /// <returns>Tray segment type</returns>
        TraySegmentType GetTraySegmentType(CableTray cableTray);

        #endregion

        #region Spatial Queries

        /// <summary>
        /// Find cable trays within a bounding box
        /// </summary>
        /// <param name="boundingBox">Search bounding box</param>
        /// <returns>List of cable trays within the bounding box</returns>
        List<CableTray> FindTraysInBoundingBox(BoundingBoxXYZ boundingBox);

        /// <summary>
        /// Find cable trays near a point
        /// </summary>
        /// <param name="point">Search point</param>
        /// <param name="searchRadius">Search radius</param>
        /// <returns>List of cable trays within the search radius</returns>
        List<CableTray> FindTraysNearPoint(XYZ point, double searchRadius);

        /// <summary>
        /// Find cable trays on a specific level
        /// </summary>
        /// <param name="level">Level to search</param>
        /// <returns>List of cable trays on the specified level</returns>
        List<CableTray> FindTraysOnLevel(Level level);

        #endregion

        #region Parameter Writing

        /// <summary>
        /// Write detected cables back to TS_Cables parameter
        /// </summary>
        /// <param name="cableTray">Cable tray element</param>
        /// <param name="cables">List of detected cables</param>
        /// <returns>True if parameter was written successfully</returns>
        bool WriteCablesParameter(CableTray cableTray, List<CableModel> cables);

        /// <summary>
        /// Write section view to TS_SectionView parameter
        /// </summary>
        /// <param name="cableTray">Cable tray element</param>
        /// <param name="sectionView">Section view</param>
        /// <returns>True if parameter was written successfully</returns>
        bool WriteSectionViewParameter(CableTray cableTray, Autodesk.Revit.DB.View sectionView);

        /// <summary>
        /// Write parametric cable to TS_ParametricCable parameter
        /// </summary>
        /// <param name="cableTray">Cable tray element</param>
        /// <param name="parametricCable">Parametric cable element</param>
        /// <returns>True if parameter was written successfully</returns>
        bool WriteParametricCableParameter(CableTray cableTray, FamilyInstance parametricCable);

        #endregion
    }
}
