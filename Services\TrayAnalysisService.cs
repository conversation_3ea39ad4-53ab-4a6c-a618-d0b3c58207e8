using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using BecaActivityLogger.CoreLogic.Data;
using BecaRevitUtilities.Extensions;
using MEP.TraylorSwift.Models;
using MEP.TraylorSwift.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using View = Autodesk.Revit.DB.View;

namespace MEP.TraylorSwift.Services
{
    /// <summary>
    /// Implementation of ITrayAnalysisService for analyzing cable trays and their geometric properties
    /// </summary>
    public class TrayAnalysisService : ITrayAnalysisService
    {
        #region Fields

        private readonly Document _document;
        private readonly BecaActivityLoggerData _logger;
        private readonly IRevitParameterService _parameterService;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the tray analysis service
        /// </summary>
        /// <param name="document">Revit document</param>
        /// <param name="logger">Activity logger</param>
        /// <param name="parameterService">Parameter service for reading/writing TS_* parameters</param>
        public TrayAnalysisService(Document document, BecaActivityLoggerData logger, IRevitParameterService parameterService)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
            _logger = logger;
            _parameterService = parameterService ?? throw new ArgumentNullException(nameof(parameterService));
        }

        #endregion

        #region Tray Discovery

        /// <summary>
        /// Find all cable trays in the document
        /// </summary>
        public List<CableTray> FindAllCableTrays()
        {
            try
            {
                var collector = new FilteredElementCollector(_document)
                    .OfCategory(BuiltInCategory.OST_CableTray)
                    .WhereElementIsNotElementType();

                var cableTrays = collector.Cast<CableTray>().ToList();
                
                _logger?.Log($"Found {cableTrays.Count} cable trays in document", LogType.Information);
                return cableTrays;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to find cable trays: {ex.Message}", LogType.Error);
                return new List<CableTray>();
            }
        }

        /// <summary>
        /// Find cable trays with existing TS_Cables parameter data
        /// </summary>
        public List<CableTray> FindCableTraysWithCableData()
        {
            try
            {
                var allTrays = FindAllCableTrays();
                var traysWithData = allTrays.Where(tray => HasRelevantData(tray)).ToList();

                bool HasRelevantData(CableTray tray)
                {
                    var cablesParam = tray.LookupParameter(TSParameterNames.TS_Cables)?.AsString(); 
                    if (!string.IsNullOrEmpty(cablesParam) && cablesParam != "-1")
                        return true;
                    var parametricCableParam = tray.LookupParameter(TSParameterNames.TS_ParametricCable)?.AsString();
                    if (!string.IsNullOrEmpty(parametricCableParam) && cablesParam != "-1")
                        return true;
                    var sectionViewParam = tray.LookupParameter(TSParameterNames.TS_SectionView)?.AsString();
                    if (!string.IsNullOrEmpty(sectionViewParam) && cablesParam != "-1")
                        return true;
                    var tagParam = tray.LookupParameter(TSParameterNames.TS_Tag)?.AsString();
                    if (!string.IsNullOrEmpty(tagParam) && cablesParam != "-1")
                        return true;

                    return false;
                }


                _logger?.Log($"Found {traysWithData.Count} cable trays with existing cable data", LogType.Information);
                return traysWithData;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to find cable trays with cable data: {ex.Message}", LogType.Error);
                return new List<CableTray>();
            }
        }

        /// <summary>
        /// Create cable tray segment model from family instance
        /// </summary>
        public CableTraySegmentModel CreateTraySegmentModel(CableTray cableTray)
        {
            try
            {
                if (cableTray == null) return null;

                var traySegment = new CableTraySegmentModel(cableTray);

                // Read existing TS_* parameters
                var cableRefsParam = _parameterService.ReadTSCablesParameter(cableTray);
                var parametricCableId = _parameterService.ReadTSParametricCableParameter(cableTray).ToElementId();
                var sectionViewId = _parameterService.ReadTSSectionViewParameter(cableTray).ToElementId();
                var tagId = cableTray.LookupParameter(TSParameterNames.TS_Tag)?.AsString().ToElementId();

                // Set parametric cable reference
                if (parametricCableId != null)
                {
                    var parametricCable = _document.GetElement(parametricCableId) as FamilyInstance;
                    if (parametricCable != null && parametricCable.IsValidObject)
                    {
                        traySegment.BecaParametricCable = parametricCable;
                    }
                }

                // Set section view reference
                if (sectionViewId != null)
                {
                    var sectionView = _document.GetElement(sectionViewId) as View;
                    if (sectionView != null && sectionView.IsValidObject)
                    {
                        traySegment.SectionView = sectionView;
                    }
                }

                // Set tag reference
                if (tagId != null)
                {
                    var tag = _document.GetElement(tagId) as IndependentTag;
                    if (tag != null && tag.IsValidObject)
                    {
                        traySegment.Tag = tag;

                        // Set Floorplan View reference
                        var view = _document.GetElement(tag.OwnerViewId) as View;
                        if (view != null && view.ViewType == ViewType.FloorPlan)
                        {
                            traySegment.FloorPlan = view;
                        }

                    }
                }

                _logger?.Log($"Created tray segment model for tray {traySegment.TrayRef}", LogType.Information);
                return traySegment;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to create tray segment model: {ex.Message}", LogType.Error);
                return null;
            }
        }

        #endregion

        #region Geometric Analysis

        /// <summary>
        /// Get the bounding box of a cable tray
        /// </summary>
        public BoundingBoxXYZ GetTrayBoundingBox(CableTray cableTray)
        {
            try
            {
                return cableTray?.get_BoundingBox(null);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to get tray bounding box: {ex.Message}", LogType.Error);
                return null;
            }
        }

        /// <summary>
        /// Get the centerline curve of a cable tray
        /// </summary>
        public Curve GetTrayCenterline(CableTray cableTray)
        {
            try
            {
                if (cableTray == null) return null;

                // Try to get the centerline from the tray's location curve
                var locationCurve = cableTray.Location as LocationCurve;
                if (locationCurve?.Curve != null)
                {
                    return locationCurve.Curve;
                }

                // Alternative: try to extract centerline from geometry
                var options = new Options();
                var geometry = cableTray.get_Geometry(options);
                
                if (geometry != null)
                {
                    foreach (GeometryObject geomObj in geometry)
                    {
                        if (geomObj is GeometryInstance geomInstance)
                        {
                            var instanceGeometry = geomInstance.GetInstanceGeometry();
                            foreach (GeometryObject instGeomObj in instanceGeometry)
                            {
                                if (instGeomObj is Curve curve)
                                {
                                    return curve;
                                }
                            }
                        }
                        else if (geomObj is Curve directCurve)
                        {
                            return directCurve;
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to get tray centerline: {ex.Message}", LogType.Error);
                return null;
            }
        }

        /// <summary>
        /// Get the cross-sectional area of a cable tray
        /// </summary>
        public double GetTrayCrossSectionalArea(CableTray cableTray)
        {
            try
            {
                if (cableTray == null) return 0.0;

                var width = _parameterService.GetParameterValueAsDouble(cableTray, "Width");
                var height = _parameterService.GetParameterValueAsDouble(cableTray, "Height");

                return width * height;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to get tray cross-sectional area: {ex.Message}", LogType.Error);
                return 0.0;
            }
        }

        /// <summary>
        /// Check if two cable trays are connected
        /// </summary>
        public bool AreTraysConnected(CableTray tray1, CableTray tray2, double tolerance = 0.1)
        {
            try
            {
                if (tray1 == null || tray2 == null || tray1.Id == tray2.Id) return false;

                // First check if they are connected via MEP connectors
                if (AreTraysConnectedByConnectors(tray1, tray2))
                {
                    return true;
                }

                // If not connected by connectors, check geometric proximity
                return AreTraysConnectedByProximity(tray1, tray2, tolerance);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to check tray connection: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Find connected cable trays
        /// </summary>
        public List<CableTray> FindConnectedTrays(CableTray cableTray, double tolerance = 0.1)
        {
            try
            {
                if (cableTray == null) return new List<CableTray>();

                var allTrays = FindAllCableTrays();
                var connectedTrays = new List<CableTray>();

                foreach (var otherTray in allTrays)
                {
                    if (otherTray.Id != cableTray.Id && AreTraysConnected(cableTray, otherTray, tolerance))
                    {
                        connectedTrays.Add(otherTray);
                    }
                }

                return connectedTrays;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to find connected trays: {ex.Message}", LogType.Error);
                return new List<CableTray>();
            }
        }

        #endregion

        #region Capacity Analysis

        /// <summary>
        /// Calculate the capacity utilization of a tray segment
        /// </summary>
        public double CalculateCapacityUtilization(CableTraySegmentModel traySegment)
        {
            try
            {
                if (traySegment?.Cables == null || traySegment.Cables.Count == 0)
                    return 0.0;

                var trayArea = traySegment.Width * traySegment.Height;
                if (trayArea <= 0) return 0.0;

                var totalCableArea = traySegment.Cables
                    .Sum(cable => Math.PI * Math.Pow(cable.Diameter / 2.0, 2));

                return Math.Min(100.0, (totalCableArea / trayArea) * 100.0);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to calculate capacity utilization: {ex.Message}", LogType.Error);
                return 0.0;
            }
        }

        /// <summary>
        /// Calculate the total weight of a tray segment including cables
        /// </summary>
        public double CalculateTotalWeight(CableTraySegmentModel traySegment)
        {
            try
            {
                if (traySegment == null) return 0.0;

                // Get tray weight from parameters
                var trayWeight = _parameterService.GetParameterValueAsDouble(traySegment.CableTray, "Weight");

                // Add cable weights
                var cableWeight = traySegment.Cables?.Sum(cable => cable.Weight) ?? 0.0;

                return trayWeight + cableWeight;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to calculate total weight: {ex.Message}", LogType.Error);
                return 0.0;
            }
        }

        /// <summary>
        /// Check if adding cables would exceed tray capacity
        /// </summary>
        public bool WouldExceedCapacity(CableTraySegmentModel traySegment, List<CableModel> additionalCables, double maxCapacityPercent = 80.0)
        {
            try
            {
                if (traySegment == null || additionalCables == null) return false;

                var trayArea = traySegment.Width * traySegment.Height;
                if (trayArea <= 0) return true;

                var currentCableArea = traySegment.Cables?.Sum(cable => Math.PI * Math.Pow(cable.Diameter / 2.0, 2)) ?? 0.0;
                var additionalCableArea = additionalCables.Sum(cable => Math.PI * Math.Pow(cable.Diameter / 2.0, 2));

                var totalCableArea = currentCableArea + additionalCableArea;
                var capacityPercent = (totalCableArea / trayArea) * 100.0;

                return capacityPercent > maxCapacityPercent;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to check capacity: {ex.Message}", LogType.Error);
                return true; // Err on the side of caution
            }
        }

        #endregion

        #region Parameter Management

        /// <summary>
        /// Read TS_Cables parameter from cable tray
        /// </summary>
        public string ReadTSCablesParameter(CableTray cableTray)
        {
            return _parameterService.ReadTSCablesParameter(cableTray);
        }

        /// <summary>
        /// Write TS_Cables parameter to cable tray
        /// </summary>
        public bool WriteTSCablesParameter(CableTray cableTray, string cableReferences)
        {
            return _parameterService.WriteTSCablesParameter(cableTray, cableReferences);
        }

        /// <summary>
        /// Read TS_ParametricCable parameter from cable tray
        /// </summary>
        public string ReadTSParametricCableParameter(CableTray cableTray)
        {
            return _parameterService.ReadTSParametricCableParameter(cableTray);
        }

        /// <summary>
        /// Write TS_ParametricCable parameter to cable tray
        /// </summary>
        public bool WriteTSParametricCableParameter(CableTray cableTray, ElementId parametricCableId)
        {
            return _parameterService.WriteTSParametricCableParameter(cableTray, parametricCableId);
        }

        /// <summary>
        /// Read TS_SectionView parameter from cable tray
        /// </summary>
        public string ReadTSSectionViewParameter(CableTray cableTray)
        {
            return _parameterService.ReadTSSectionViewParameter(cableTray);
        }

        /// <summary>
        /// Write TS_SectionView parameter to cable tray
        /// </summary>
        public bool WriteTSSectionViewParameter(CableTray cableTray, ElementId sectionViewId)
        {
            return _parameterService.WriteTSSectionViewParameter(cableTray, sectionViewId);
        }

        /// <summary>
        /// Write TS_SectionView parameter to cable tray
        /// </summary>
        public bool WriteTSTagParameter(CableTray cableTray, ElementId sectionViewId)
        {
            return _parameterService.WriteTSTagParameter(cableTray, sectionViewId);
        }

        #endregion

        #region Validation

        /// <summary>
        /// Validate cable tray segment data
        /// </summary>
        public List<string> ValidateTraySegment(CableTraySegmentModel traySegment)
        {
            var issues = new List<string>();

            try
            {
                if (traySegment == null)
                {
                    issues.Add("Tray segment is null");
                    return issues;
                }

                if (traySegment.CableTray == null || !traySegment.CableTray.IsValidObject)
                {
                    issues.Add("Cable tray element is invalid");
                }

                if (string.IsNullOrEmpty(traySegment.TrayRef))
                {
                    issues.Add("Tray reference is empty");
                }

                if (traySegment.Width <= 0)
                {
                    issues.Add("Tray width is invalid");
                }

                if (traySegment.Height <= 0)
                {
                    issues.Add("Tray height is invalid");
                }

                if (traySegment.Capacity > 100)
                {
                    issues.Add("Capacity exceeds 100%");
                }

                if (traySegment.BecaParametricCable != null && !traySegment.BecaParametricCable.IsValidObject)
                {
                    issues.Add("Parametric cable reference is invalid");
                }

                if (traySegment.SectionView != null && !traySegment.SectionView.IsValidObject)
                {
                    issues.Add("Section view reference is invalid");
                }
            }
            catch (Exception ex)
            {
                issues.Add($"Validation error: {ex.Message}");
            }

            return issues;
        }

        /// <summary>
        /// Check if cable tray is valid for processing
        /// </summary>
        public bool IsValidCableTray(CableTray cableTray)
        {
            try
            {
                return cableTray != null && 
                       cableTray.IsValidObject && 
                       cableTray.Category?.Id?.IntegerValue() == (int)BuiltInCategory.OST_CableTray;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Get tray segment type based on geometry
        /// </summary>
        public TraySegmentType GetTraySegmentType(CableTray cableTray)
        {
            try
            {
                if (cableTray == null) return TraySegmentType.Unknown;

                var centerline = GetTrayCenterline(cableTray);
                if (centerline == null) return TraySegmentType.Unknown;

                if (centerline is Line)
                {
                    return TraySegmentType.Straight;
                }
                else if (centerline is Arc)
                {
                    return TraySegmentType.Curved;
                }
                else
                {
                    // Check if it's a fitting based on family name or type
                    var familyName = cableTray.Name?.ToLowerInvariant() ?? "";
                    if (familyName.Contains("fitting") || familyName.Contains("tee") || 
                        familyName.Contains("cross") || familyName.Contains("reducer"))
                    {
                        return TraySegmentType.Fitting;
                    }

                    return TraySegmentType.Curved;
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to determine tray segment type: {ex.Message}", LogType.Error);
                return TraySegmentType.Unknown;
            }
        }

        #endregion

        #region Spatial Queries

        /// <summary>
        /// Find cable trays within a bounding box
        /// </summary>
        public List<CableTray> FindTraysInBoundingBox(BoundingBoxXYZ boundingBox)
        {
            try
            {
                if (boundingBox == null) return new List<CableTray>();

                var allTrays = FindAllCableTrays();
                return allTrays.Where(tray =>
                {
                    var trayBBox = GetTrayBoundingBox(tray);
                    return trayBBox != null && BoundingBoxesIntersect(trayBBox, boundingBox);
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to find trays in bounding box: {ex.Message}", LogType.Error);
                return new List<CableTray>();
            }
        }

        /// <summary>
        /// Find cable trays near a point
        /// </summary>
        public List<CableTray> FindTraysNearPoint(XYZ point, double searchRadius)
        {
            try
            {
                if (point == null) return new List<CableTray>();

                var searchBox = new BoundingBoxXYZ
                {
                    Min = point - new XYZ(searchRadius, searchRadius, searchRadius),
                    Max = point + new XYZ(searchRadius, searchRadius, searchRadius)
                };

                return FindTraysInBoundingBox(searchBox);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to find trays near point: {ex.Message}", LogType.Error);
                return new List<CableTray>();
            }
        }

        /// <summary>
        /// Find cable trays on a specific level
        /// </summary>
        public List<CableTray> FindTraysOnLevel(Level level)
        {
            try
            {
                if (level == null) return new List<CableTray>();

                var allTrays = FindAllCableTrays();
                return allTrays.Where(tray => tray.LevelId == level.Id).ToList();
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to find trays on level: {ex.Message}", LogType.Error);
                return new List<CableTray>();
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Check if two trays are connected via MEP connectors
        /// </summary>
        private bool AreTraysConnectedByConnectors(CableTray tray1, CableTray tray2)
        {
            try
            {
                var connectors1 = GetTrayConnectors(tray1);
                var connectors2 = GetTrayConnectors(tray2);

                foreach (var conn1 in connectors1)
                {
                    foreach (var conn2 in connectors2)
                    {
                        if (conn1.IsConnectedTo(conn2))
                        {
                            return true;
                        }
                    }
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Check if two trays are connected by geometric proximity
        /// </summary>
        private bool AreTraysConnectedByProximity(CableTray tray1, CableTray tray2, double tolerance)
        {
            try
            {
                var bbox1 = GetTrayBoundingBox(tray1);
                var bbox2 = GetTrayBoundingBox(tray2);

                if (bbox1 == null || bbox2 == null) return false;

                // Check if bounding boxes are within tolerance
                return BoundingBoxesIntersect(bbox1, bbox2, tolerance);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Get connectors from a cable tray
        /// </summary>
        private List<Connector> GetTrayConnectors(CableTray cableTray)
        {
            var connectors = new List<Connector>();

            try
            {
                var connectorManager = cableTray.ConnectorManager;
                if (connectorManager != null)
                {
                    foreach (Connector connector in connectorManager.Connectors)
                    {
                        connectors.Add(connector);
                    }
                }
            }
            catch
            {
                // Ignore errors in connector access
            }

            return connectors;
        }

        /// <summary>
        /// Check if two bounding boxes intersect within tolerance
        /// </summary>
        private bool BoundingBoxesIntersect(BoundingBoxXYZ bbox1, BoundingBoxXYZ bbox2, double tolerance = 0.0)
        {
            return bbox1.Max.X + tolerance >= bbox2.Min.X && bbox1.Min.X - tolerance <= bbox2.Max.X &&
                   bbox1.Max.Y + tolerance >= bbox2.Min.Y && bbox1.Min.Y - tolerance <= bbox2.Max.Y &&
                   bbox1.Max.Z + tolerance >= bbox2.Min.Z && bbox1.Min.Z - tolerance <= bbox2.Max.Z;
        }

        #endregion

        #region Parameter Writing

        /// <summary>
        /// Write detected cables back to TS_Cables parameter
        /// </summary>
        public bool WriteCablesParameter(CableTray cableTray, List<CableModel> cables)
        {
            try
            {
                if (cableTray == null || cables == null) return false;

                var cableRefs = string.Join(";", cables.Select(c => c.CableRef ?? ""));
                return SetParameterValue(cableTray, "TS_Cables", cableRefs);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to write cables parameter: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Write section view to TS_SectionView parameter
        /// </summary>
        public bool WriteSectionViewParameter(CableTray cableTray, View sectionView)
        {
            try
            {
                if (cableTray == null || sectionView == null) return false;

                return SetParameterValue(cableTray, TSParameterNames.TS_SectionView, sectionView.Id.ToString());
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to write section view parameter: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Write parametric cable to TS_ParametricCable parameter
        /// </summary>
        public bool WriteParametricCableParameter(CableTray cableTray, FamilyInstance parametricCable)
        {
            try
            {
                if (cableTray == null || parametricCable == null) return false;

                return SetParameterValue(cableTray, "TS_ParametricCable", parametricCable.Id.ToString());
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to write parametric cable parameter: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Set parameter value on element
        /// </summary>
        private bool SetParameterValue(Element element, string parameterName, string value)
        {
            try
            {
                var parameter = element.LookupParameter(parameterName);
                if (parameter != null && !parameter.IsReadOnly)
                {
                    parameter.Set(value ?? "");
                    return true;
                }
                else
                {
                    _logger?.Log($"Parameter '{parameterName}' not found or is read-only", LogType.Warning);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to set parameter '{parameterName}': {ex.Message}", LogType.Error);
                return false;
            }
        }

        #endregion
    }
}
