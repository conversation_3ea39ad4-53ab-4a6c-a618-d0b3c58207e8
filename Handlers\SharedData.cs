using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using MEP.TraylorSwift.Models;

namespace MEP.TraylorSwift.Handlers
{
    /// <summary>
    /// Shared data for communication between request handlers and ViewModels
    /// </summary>
    public static class SharedData
    {
        /// <summary>
        /// Selected tray for processing
        /// </summary>
        public static CableTray SelectedTrayForProcessing { get; set; }

        /// <summary>
        /// Current processing action
        /// </summary>
        public static string ProcessingAction { get; set; }

        /// <summary>
        /// Selected cable for processing
        /// </summary>
        public static CableModel SelectedCableForProcessing { get; set; }

        /// <summary>
        /// Selected tray segment for processing
        /// </summary>
        public static CableTraySegmentModel SelectedTraySegmentForProcessing { get; set; }

        /// <summary>
        /// Section view name for creation
        /// </summary>
        public static string SectionViewName { get; set; }

        /// <summary>
        /// Parametric cable configuration
        /// </summary>
        public static ParametricCableConfiguration ParametricCableConfig { get; set; }

        /// <summary>
        /// Clear all shared data
        /// </summary>
        public static void Clear()
        {
            SelectedTrayForProcessing = null;
            ProcessingAction = null;
            SelectedCableForProcessing = null;
            SelectedTraySegmentForProcessing = null;
            SectionViewName = null;
            ParametricCableConfig = null;
        }
    }

    /// <summary>
    /// Configuration for parametric cable parameters
    /// </summary>
    public class ParametricCableConfiguration
    {
        public double MinimumSpaceCapacity { get; set; } = 80.0;
        public string CableArrangement { get; set; } = "flat";
        public string CableSpacing { get; set; } = "Touching";
    }
}
