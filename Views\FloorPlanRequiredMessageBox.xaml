﻿<Window x:Class="MEP.TraylorSwift.Views.FloorPlanRequiredMessageBox"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MEP.TraylorSwift.Views"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="FloorPlanRequiredMessageBox" Height="450" Width="800">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Icon and Title -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
            <materialDesign:PackIcon Kind="Information" 
                                   Width="32" 
                                   Height="32" 
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,0"/>
            <TextBlock Text="Floor Plan Required" 
                      FontSize="18" 
                      FontWeight="SemiBold"
                      VerticalAlignment="Center"/>
        </StackPanel>

        <!-- Message -->
        <TextBlock Grid.Row="1" 
                  Text="Tags can only be created in Floor Plan views. Please switch to a Floor Plan view and try again."
                  TextWrapping="Wrap"
                  FontSize="14"
                  Margin="0,0,0,15"/>

        <!-- Don't show again checkbox -->
        <CheckBox Grid.Row="2" 
                 x:Name="DontShowAgainCheckBox"
                 Content="Don't show this message again"
                 FontSize="12"
                 Margin="0,0,0,15"/>

        <!-- Buttons -->
        <StackPanel Grid.Row="3" 
                   Orientation="Horizontal" 
                   HorizontalAlignment="Right">
            <Button Content="OK" 
                   Width="80" 
                   Height="35"
                   IsDefault="True"
                   Click="OkButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
