﻿using Autodesk.Revit.DB;
using MEP.TraylorSwift.Models;
using System.Collections.Generic;

namespace MEP.TraylorSwift.Services.Interfaces
{
    /// <summary>
    /// Service for creating and managing cable visualization in Revit 3D views
    /// </summary>
    public interface ICableVisualizationService
    {
        /// <summary>
        /// Create purple cable lines from cable route points in a 3D view
        /// </summary>
        /// <param name="view3D">Target 3D view</param>
        /// <param name="cable">Cable model with route points</param>
        /// <returns>List of created DirectShape elements representing the cable run</returns>
        List<DirectShape> CreateCableLines(View3D view3D, CableModel cable);

        /// <summary>
        /// Remove all existing cable lines from the specified view
        /// </summary>
        /// <param name="view3D">Target 3D view</param>
        /// <param name="userName">User name to identify cable lines to remove</param>
        void ClearExistingCableLines(View3D view3D, string userName);

        /// <summary>
        /// Set graphics overrides for cable run elements in the view
        /// </summary>
        /// <param name="view3D">Target 3D view</param>
        /// <param name="cableLineElements">Cable line elements to highlight</param>
        void SetCableLineGraphicsOverrides(View3D view3D, List<DirectShape> cableLineElements);

        /// <summary>
        /// Calculate bounding box for cable route points
        /// </summary>
        /// <param name="routePoints">Cable route points</param>
        /// <param name="margin">Margin to add around the bounding box</param>
        /// <returns>Expanded bounding box for the cable route</returns>
        BoundingBoxXYZ CalculateCableRouteBoundingBox(List<XYZ> routePoints, double margin = 5.0);
    }
}

