using Autodesk.Revit.DB;
using BecaActivityLogger.CoreLogic.Data;
using MEP.TraylorSwift.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace MEP.TraylorSwift.Services
{
    /// <summary>
    /// Service for advanced geometric analysis algorithms used in cable-tray intersection detection
    /// </summary>
    public class GeometricAnalysisService
    {
        #region Fields

        private readonly BecaActivityLoggerData _logger;
        private const double DefaultTolerance = 0.1; // 0.1 feet tolerance

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the geometric analysis service
        /// </summary>
        /// <param name="logger">Activity logger</param>
        public GeometricAnalysisService(BecaActivityLoggerData logger)
        {
            _logger = logger;
        }

        #endregion

        #region Line-Box Intersection

        /// <summary>
        /// Precise line-box intersection test using parametric line equation
        /// </summary>
        /// <param name="lineStart">Start point of line</param>
        /// <param name="lineEnd">End point of line</param>
        /// <param name="boundingBox">Bounding box to test intersection with</param>
        /// <param name="tolerance">Intersection tolerance</param>
        /// <returns>True if line intersects bounding box</returns>
        public bool LineIntersectsBoundingBox(XYZ lineStart, XYZ lineEnd, BoundingBoxXYZ boundingBox, double tolerance = DefaultTolerance)
        {
            try
            {
                if (lineStart == null || lineEnd == null || boundingBox == null)
                    return false;

                // Expand bounding box by tolerance
                var min = boundingBox.Min - new XYZ(tolerance, tolerance, tolerance);
                var max = boundingBox.Max + new XYZ(tolerance, tolerance, tolerance);

                // Check if either endpoint is inside the expanded box
                if (IsPointInBoundingBox(lineStart, min, max) || IsPointInBoundingBox(lineEnd, min, max))
                    return true;

                // Use parametric line equation: P(t) = lineStart + t * (lineEnd - lineStart)
                var direction = lineEnd - lineStart;
                var length = direction.GetLength();
                
                if (length < 1e-6) return false; // Degenerate line

                direction = direction.Normalize();

                // Calculate intersection parameters for each axis
                double tMin = 0.0;
                double tMax = length;

                for (int axis = 0; axis < 3; axis++)
                {
                    double startCoord = GetCoordinate(lineStart, axis);
                    double dirCoord = GetCoordinate(direction, axis);
                    double minCoord = GetCoordinate(min, axis);
                    double maxCoord = GetCoordinate(max, axis);

                    if (Math.Abs(dirCoord) < 1e-6)
                    {
                        // Line is parallel to this axis
                        if (startCoord < minCoord || startCoord > maxCoord)
                            return false; // Line is outside box on this axis
                    }
                    else
                    {
                        // Calculate intersection parameters
                        double t1 = (minCoord - startCoord) / dirCoord;
                        double t2 = (maxCoord - startCoord) / dirCoord;

                        if (t1 > t2) { double temp = t1; t1 = t2; t2 = temp; }

                        tMin = Math.Max(tMin, t1);
                        tMax = Math.Min(tMax, t2);

                        if (tMin > tMax) return false; // No intersection
                    }
                }

                return tMin <= tMax && tMax >= 0 && tMin <= length;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error in line-box intersection test: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Find intersection points between a line and a bounding box
        /// </summary>
        /// <param name="lineStart">Start point of line</param>
        /// <param name="lineEnd">End point of line</param>
        /// <param name="boundingBox">Bounding box</param>
        /// <param name="tolerance">Intersection tolerance</param>
        /// <returns>List of intersection points</returns>
        public List<XYZ> FindLineBoxIntersectionPoints(XYZ lineStart, XYZ lineEnd, BoundingBoxXYZ boundingBox, double tolerance = DefaultTolerance)
        {
            var intersectionPoints = new List<XYZ>();

            try
            {
                if (!LineIntersectsBoundingBox(lineStart, lineEnd, boundingBox, tolerance))
                    return intersectionPoints;

                var direction = lineEnd - lineStart;
                var length = direction.GetLength();
                
                if (length < 1e-6) return intersectionPoints;

                direction = direction.Normalize();

                // Expand bounding box by tolerance
                var min = boundingBox.Min - new XYZ(tolerance, tolerance, tolerance);
                var max = boundingBox.Max + new XYZ(tolerance, tolerance, tolerance);

                var tValues = new List<double>();

                // Find intersection parameters for each face of the box
                for (int axis = 0; axis < 3; axis++)
                {
                    double startCoord = GetCoordinate(lineStart, axis);
                    double dirCoord = GetCoordinate(direction, axis);
                    double minCoord = GetCoordinate(min, axis);
                    double maxCoord = GetCoordinate(max, axis);

                    if (Math.Abs(dirCoord) > 1e-6)
                    {
                        double t1 = (minCoord - startCoord) / dirCoord;
                        double t2 = (maxCoord - startCoord) / dirCoord;

                        if (t1 >= 0 && t1 <= length) tValues.Add(t1);
                        if (t2 >= 0 && t2 <= length) tValues.Add(t2);
                    }
                }

                // Convert parameters to points and filter valid intersections
                foreach (double t in tValues.Distinct())
                {
                    var point = lineStart + direction * t;
                    if (IsPointInBoundingBox(point, min, max))
                    {
                        intersectionPoints.Add(point);
                    }
                }

                // Remove duplicate points
                intersectionPoints = RemoveDuplicatePoints(intersectionPoints, tolerance);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error finding line-box intersection points: {ex.Message}", LogType.Error);
            }

            return intersectionPoints;
        }

        #endregion

        #region Curve-Box Intersection

        /// <summary>
        /// Test intersection between a curve and a bounding box
        /// </summary>
        /// <param name="curve">Curve to test</param>
        /// <param name="boundingBox">Bounding box</param>
        /// <param name="tolerance">Intersection tolerance</param>
        /// <param name="subdivisions">Number of subdivisions for curve approximation</param>
        /// <returns>True if curve intersects bounding box</returns>
        public bool CurveIntersectsBoundingBox(Curve curve, BoundingBoxXYZ boundingBox, double tolerance = DefaultTolerance, int subdivisions = 20)
        {
            try
            {
                if (curve == null || boundingBox == null) return false;

                // First check if curve bounding box intersects with target bounding box
                var curveBBox = CalculateCurveBoundingBox(curve);
                if (curveBBox != null && !BoundingBoxesIntersect(curveBBox, boundingBox, tolerance))
                    return false;

                // For lines, use precise line-box intersection
                if (curve is Line line)
                {
                    return LineIntersectsBoundingBox(line.GetEndPoint(0), line.GetEndPoint(1), boundingBox, tolerance);
                }

                // For other curves, subdivide and test line segments
                double parameterRange = curve.GetEndParameter(1) - curve.GetEndParameter(0);
                double stepSize = parameterRange / subdivisions;

                for (int i = 0; i < subdivisions; i++)
                {
                    double t1 = curve.GetEndParameter(0) + i * stepSize;
                    double t2 = curve.GetEndParameter(0) + (i + 1) * stepSize;

                    var point1 = curve.Evaluate(t1, false);
                    var point2 = curve.Evaluate(t2, false);

                    if (LineIntersectsBoundingBox(point1, point2, boundingBox, tolerance))
                        return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error in curve-box intersection test: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Find intersection points between a curve and a bounding box
        /// </summary>
        /// <param name="curve">Curve to intersect</param>
        /// <param name="boundingBox">Bounding box</param>
        /// <param name="tolerance">Intersection tolerance</param>
        /// <param name="subdivisions">Number of subdivisions for curve approximation</param>
        /// <returns>List of intersection points</returns>
        public List<XYZ> FindCurveBoxIntersectionPoints(Curve curve, BoundingBoxXYZ boundingBox, double tolerance = DefaultTolerance, int subdivisions = 50)
        {
            var intersectionPoints = new List<XYZ>();

            try
            {
                if (curve == null || boundingBox == null) return intersectionPoints;

                // For lines, use precise intersection
                if (curve is Line line)
                {
                    return FindLineBoxIntersectionPoints(line.GetEndPoint(0), line.GetEndPoint(1), boundingBox, tolerance);
                }

                // For other curves, subdivide and find intersections
                double parameterRange = curve.GetEndParameter(1) - curve.GetEndParameter(0);
                double stepSize = parameterRange / subdivisions;

                for (int i = 0; i < subdivisions; i++)
                {
                    double t1 = curve.GetEndParameter(0) + i * stepSize;
                    double t2 = curve.GetEndParameter(0) + (i + 1) * stepSize;

                    var point1 = curve.Evaluate(t1, false);
                    var point2 = curve.Evaluate(t2, false);

                    var segmentIntersections = FindLineBoxIntersectionPoints(point1, point2, boundingBox, tolerance);
                    intersectionPoints.AddRange(segmentIntersections);
                }

                // Remove duplicate points
                intersectionPoints = RemoveDuplicatePoints(intersectionPoints, tolerance);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error finding curve-box intersection points: {ex.Message}", LogType.Error);
            }

            return intersectionPoints;
        }

        #endregion

        #region Point-Box Operations

        /// <summary>
        /// Check if a point is inside a bounding box
        /// </summary>
        /// <param name="point">Point to test</param>
        /// <param name="boxMin">Minimum corner of bounding box</param>
        /// <param name="boxMax">Maximum corner of bounding box</param>
        /// <returns>True if point is inside bounding box</returns>
        public bool IsPointInBoundingBox(XYZ point, XYZ boxMin, XYZ boxMax)
        {
            if (point == null || boxMin == null || boxMax == null) return false;

            return point.X >= boxMin.X && point.X <= boxMax.X &&
                   point.Y >= boxMin.Y && point.Y <= boxMax.Y &&
                   point.Z >= boxMin.Z && point.Z <= boxMax.Z;
        }

        /// <summary>
        /// Check if a point is inside a bounding box with tolerance
        /// </summary>
        /// <param name="point">Point to test</param>
        /// <param name="boundingBox">Bounding box</param>
        /// <param name="tolerance">Tolerance for inclusion test</param>
        /// <returns>True if point is inside bounding box within tolerance</returns>
        public bool IsPointInBoundingBox(XYZ point, BoundingBoxXYZ boundingBox, double tolerance = DefaultTolerance)
        {
            if (point == null || boundingBox == null) return false;

            var min = boundingBox.Min - new XYZ(tolerance, tolerance, tolerance);
            var max = boundingBox.Max + new XYZ(tolerance, tolerance, tolerance);

            return IsPointInBoundingBox(point, min, max);
        }

        /// <summary>
        /// Calculate the closest point on a bounding box to a given point
        /// </summary>
        /// <param name="point">Point to find closest point to</param>
        /// <param name="boundingBox">Bounding box</param>
        /// <returns>Closest point on bounding box</returns>
        public XYZ GetClosestPointOnBoundingBox(XYZ point, BoundingBoxXYZ boundingBox)
        {
            if (point == null || boundingBox == null) return null;

            var closestX = Math.Max(boundingBox.Min.X, Math.Min(point.X, boundingBox.Max.X));
            var closestY = Math.Max(boundingBox.Min.Y, Math.Min(point.Y, boundingBox.Max.Y));
            var closestZ = Math.Max(boundingBox.Min.Z, Math.Min(point.Z, boundingBox.Max.Z));

            return new XYZ(closestX, closestY, closestZ);
        }

        /// <summary>
        /// Calculate distance from a point to a bounding box
        /// </summary>
        /// <param name="point">Point to calculate distance from</param>
        /// <param name="boundingBox">Bounding box</param>
        /// <returns>Distance from point to bounding box (0 if point is inside)</returns>
        public double DistanceFromPointToBoundingBox(XYZ point, BoundingBoxXYZ boundingBox)
        {
            if (point == null || boundingBox == null) return double.MaxValue;

            var closestPoint = GetClosestPointOnBoundingBox(point, boundingBox);
            return closestPoint?.DistanceTo(point) ?? double.MaxValue;
        }

        #endregion

        #region Bounding Box Operations

        /// <summary>
        /// Check if two bounding boxes intersect
        /// </summary>
        /// <param name="bbox1">First bounding box</param>
        /// <param name="bbox2">Second bounding box</param>
        /// <param name="tolerance">Intersection tolerance</param>
        /// <returns>True if bounding boxes intersect</returns>
        public bool BoundingBoxesIntersect(BoundingBoxXYZ bbox1, BoundingBoxXYZ bbox2, double tolerance = DefaultTolerance)
        {
            if (bbox1 == null || bbox2 == null) return false;

            return bbox1.Max.X + tolerance >= bbox2.Min.X && bbox1.Min.X - tolerance <= bbox2.Max.X &&
                   bbox1.Max.Y + tolerance >= bbox2.Min.Y && bbox1.Min.Y - tolerance <= bbox2.Max.Y &&
                   bbox1.Max.Z + tolerance >= bbox2.Min.Z && bbox1.Min.Z - tolerance <= bbox2.Max.Z;
        }

        /// <summary>
        /// Calculate the intersection of two bounding boxes
        /// </summary>
        /// <param name="bbox1">First bounding box</param>
        /// <param name="bbox2">Second bounding box</param>
        /// <returns>Intersection bounding box or null if no intersection</returns>
        public BoundingBoxXYZ GetBoundingBoxIntersection(BoundingBoxXYZ bbox1, BoundingBoxXYZ bbox2)
        {
            if (bbox1 == null || bbox2 == null || !BoundingBoxesIntersect(bbox1, bbox2))
                return null;

            var min = new XYZ(
                Math.Max(bbox1.Min.X, bbox2.Min.X),
                Math.Max(bbox1.Min.Y, bbox2.Min.Y),
                Math.Max(bbox1.Min.Z, bbox2.Min.Z)
            );

            var max = new XYZ(
                Math.Min(bbox1.Max.X, bbox2.Max.X),
                Math.Min(bbox1.Max.Y, bbox2.Max.Y),
                Math.Min(bbox1.Max.Z, bbox2.Max.Z)
            );

            return new BoundingBoxXYZ { Min = min, Max = max };
        }

        /// <summary>
        /// Calculate the union of two bounding boxes
        /// </summary>
        /// <param name="bbox1">First bounding box</param>
        /// <param name="bbox2">Second bounding box</param>
        /// <returns>Union bounding box</returns>
        public BoundingBoxXYZ GetBoundingBoxUnion(BoundingBoxXYZ bbox1, BoundingBoxXYZ bbox2)
        {
            if (bbox1 == null) return bbox2;
            if (bbox2 == null) return bbox1;

            var min = new XYZ(
                Math.Min(bbox1.Min.X, bbox2.Min.X),
                Math.Min(bbox1.Min.Y, bbox2.Min.Y),
                Math.Min(bbox1.Min.Z, bbox2.Min.Z)
            );

            var max = new XYZ(
                Math.Max(bbox1.Max.X, bbox2.Max.X),
                Math.Max(bbox1.Max.Y, bbox2.Max.Y),
                Math.Max(bbox1.Max.Z, bbox2.Max.Z)
            );

            return new BoundingBoxXYZ { Min = min, Max = max };
        }

        /// <summary>
        /// Expand a bounding box by a specified amount
        /// </summary>
        /// <param name="boundingBox">Bounding box to expand</param>
        /// <param name="expansion">Amount to expand in all directions</param>
        /// <returns>Expanded bounding box</returns>
        public BoundingBoxXYZ ExpandBoundingBox(BoundingBoxXYZ boundingBox, double expansion)
        {
            if (boundingBox == null) return null;

            var expansionVector = new XYZ(expansion, expansion, expansion);
            return new BoundingBoxXYZ
            {
                Min = boundingBox.Min - expansionVector,
                Max = boundingBox.Max + expansionVector
            };
        }

        #endregion

        #region Connectivity Analysis

        /// <summary>
        /// Check if two elements are geometrically connected within tolerance
        /// </summary>
        /// <param name="element1">First element</param>
        /// <param name="element2">Second element</param>
        /// <param name="tolerance">Connection tolerance</param>
        /// <returns>True if elements are connected</returns>
        public bool AreElementsConnected(Element element1, Element element2, double tolerance = DefaultTolerance)
        {
            try
            {
                if (element1 == null || element2 == null || element1.Id == element2.Id)
                    return false;

                var bbox1 = element1.get_BoundingBox(null);
                var bbox2 = element2.get_BoundingBox(null);

                if (bbox1 == null || bbox2 == null) return false;

                // Check if bounding boxes are within tolerance
                return BoundingBoxesIntersect(bbox1, bbox2, tolerance);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error checking element connectivity: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Find connection points between two elements
        /// </summary>
        /// <param name="element1">First element</param>
        /// <param name="element2">Second element</param>
        /// <param name="tolerance">Connection tolerance</param>
        /// <returns>List of connection points</returns>
        public List<XYZ> FindConnectionPoints(Element element1, Element element2, double tolerance = DefaultTolerance)
        {
            var connectionPoints = new List<XYZ>();

            try
            {
                if (!AreElementsConnected(element1, element2, tolerance))
                    return connectionPoints;

                var bbox1 = element1.get_BoundingBox(null);
                var bbox2 = element2.get_BoundingBox(null);

                if (bbox1 == null || bbox2 == null) return connectionPoints;

                // Find intersection of bounding boxes
                var intersection = GetBoundingBoxIntersection(bbox1, bbox2);
                if (intersection != null)
                {
                    // Add center of intersection as connection point
                    var center = (intersection.Min + intersection.Max) * 0.5;
                    connectionPoints.Add(center);
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error finding connection points: {ex.Message}", LogType.Error);
            }

            return connectionPoints;
        }

        #endregion

        #region Curve Operations

        /// <summary>
        /// Calculate bounding box for a curve by sampling points
        /// </summary>
        /// <param name="curve">Curve to calculate bounding box for</param>
        /// <param name="sampleCount">Number of points to sample along curve</param>
        /// <returns>Bounding box of the curve</returns>
        public BoundingBoxXYZ CalculateCurveBoundingBox(Curve curve, int sampleCount = 50)
        {
            try
            {
                if (curve == null) return null;

                var points = new List<XYZ>();

                // Add start and end points
                points.Add(curve.GetEndPoint(0));
                points.Add(curve.GetEndPoint(1));

                // Sample points along the curve
                double parameterRange = curve.GetEndParameter(1) - curve.GetEndParameter(0);
                double stepSize = parameterRange / (sampleCount - 1);

                for (int i = 1; i < sampleCount - 1; i++)
                {
                    double parameter = curve.GetEndParameter(0) + i * stepSize;
                    try
                    {
                        var point = curve.Evaluate(parameter, false);
                        points.Add(point);
                    }
                    catch
                    {
                        // Skip invalid parameters
                    }
                }

                // Calculate bounding box from all points
                if (points.Count == 0) return null;

                var min = new XYZ(
                    points.Min(p => p.X),
                    points.Min(p => p.Y),
                    points.Min(p => p.Z)
                );

                var max = new XYZ(
                    points.Max(p => p.X),
                    points.Max(p => p.Y),
                    points.Max(p => p.Z)
                );

                return new BoundingBoxXYZ { Min = min, Max = max };
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error calculating curve bounding box: {ex.Message}", LogType.Error);
                return null;
            }
        }

        /// <summary>
        /// Get points along a curve at specified intervals
        /// </summary>
        /// <param name="curve">Curve to sample</param>
        /// <param name="sampleCount">Number of points to sample</param>
        /// <returns>List of points along the curve</returns>
        public List<XYZ> SampleCurvePoints(Curve curve, int sampleCount = 20)
        {
            var points = new List<XYZ>();

            try
            {
                if (curve == null || sampleCount < 2) return points;

                double parameterRange = curve.GetEndParameter(1) - curve.GetEndParameter(0);
                double stepSize = parameterRange / (sampleCount - 1);

                for (int i = 0; i < sampleCount; i++)
                {
                    double parameter = curve.GetEndParameter(0) + i * stepSize;
                    try
                    {
                        var point = curve.Evaluate(parameter, false);
                        points.Add(point);
                    }
                    catch
                    {
                        // Skip invalid parameters
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error sampling curve points: {ex.Message}", LogType.Error);
            }

            return points;
        }
        #endregion

        #region Utility Methods

        /// <summary>
        /// Get coordinate value by axis index (0=X, 1=Y, 2=Z)
        /// </summary>
        /// <param name="point">Point to get coordinate from</param>
        /// <param name="axis">Axis index (0=X, 1=Y, 2=Z)</param>
        /// <returns>Coordinate value</returns>
        public double GetCoordinate(XYZ point, int axis)
        {
            if (point == null) return 0.0;

            switch (axis)
            {
                case 0: return point.X;
                case 1: return point.Y;
                case 2: return point.Z;
                default: return 0.0;
            }
        }

        /// <summary>
        /// Remove duplicate points from a list within tolerance
        /// </summary>
        /// <param name="points">List of points</param>
        /// <param name="tolerance">Tolerance for considering points duplicate</param>
        /// <returns>List with duplicate points removed</returns>
        public List<XYZ> RemoveDuplicatePoints(List<XYZ> points, double tolerance = DefaultTolerance)
        {
            if (points == null || points.Count == 0) return new List<XYZ>();

            var uniquePoints = new List<XYZ>();

            foreach (var point in points)
            {
                bool isDuplicate = false;
                foreach (var existingPoint in uniquePoints)
                {
                    if (point.DistanceTo(existingPoint) < tolerance)
                    {
                        isDuplicate = true;
                        break;
                    }
                }

                if (!isDuplicate)
                {
                    uniquePoints.Add(point);
                }
            }

            return uniquePoints;
        }

        /// <summary>
        /// Calculate the volume of a bounding box
        /// </summary>
        /// <param name="boundingBox">Bounding box</param>
        /// <returns>Volume of bounding box</returns>
        public double CalculateBoundingBoxVolume(BoundingBoxXYZ boundingBox)
        {
            if (boundingBox == null) return 0.0;

            var dimensions = boundingBox.Max - boundingBox.Min;
            return dimensions.X * dimensions.Y * dimensions.Z;
        }

        /// <summary>
        /// Calculate the surface area of a bounding box
        /// </summary>
        /// <param name="boundingBox">Bounding box</param>
        /// <returns>Surface area of bounding box</returns>
        public double CalculateBoundingBoxSurfaceArea(BoundingBoxXYZ boundingBox)
        {
            if (boundingBox == null) return 0.0;

            var dimensions = boundingBox.Max - boundingBox.Min;
            return 2.0 * (dimensions.X * dimensions.Y + dimensions.Y * dimensions.Z + dimensions.Z * dimensions.X);
        }

        /// <summary>
        /// Get the center point of a bounding box
        /// </summary>
        /// <param name="boundingBox">Bounding box</param>
        /// <returns>Center point</returns>
        public XYZ GetBoundingBoxCenter(BoundingBoxXYZ boundingBox)
        {
            if (boundingBox == null) return null;
            return (boundingBox.Min + boundingBox.Max) * 0.5;
        }

        /// <summary>
        /// Get the diagonal length of a bounding box
        /// </summary>
        /// <param name="boundingBox">Bounding box</param>
        /// <returns>Diagonal length</returns>
        public double GetBoundingBoxDiagonal(BoundingBoxXYZ boundingBox)
        {
            if (boundingBox == null) return 0.0;
            return boundingBox.Min.DistanceTo(boundingBox.Max);
        }

        #endregion
    }
}
