﻿using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using MEP.TraylorSwift.Services.Interfaces;
using MEP.TraylorSwift.ViewModels;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Navigation;

namespace MEP.TraylorSwift.Services
{
    /// <summary>
    /// Service configuration for dependency injection container
    /// Configures all services and ViewModels for WPF application
    /// </summary>
    public static class ServiceConfiguration
    {
        /// <summary>
        /// Configure all services for the application
        /// </summary>
        /// <param name="services">Service collection to configure</param>
        /// <param name="uiDocument">Current UI document</param>
        /// <param name="logger">Activity logger</param>
        /// <returns>Configured service collection</returns>
        public static IServiceCollection ConfigureServices(this IServiceCollection services, UIDocument uiDocument, BecaActivityLoggerData logger)
        {
            // Register core dependencies
            services.AddSingleton(uiDocument);
            services.AddSingleton(uiDocument.Document);
            services.AddSingleton(uiDocument.Application);
            services.AddSingleton(logger);

            // Register business services
            RegisterBusinessServices(services);

            // Register ViewModels
            RegisterViewModels(services);

            return services;
        }

        /// <summary>
        /// Register all business services
        /// </summary>
        /// <param name="services">Service collection</param>
        private static void RegisterBusinessServices(IServiceCollection services)
        {
            // Core services
            services.AddTransient<IRevitService, RevitService>();

            // Parameter services
            services.AddTransient<IRevitParameterService, RevitParameterService>();

            // Spatial indexing services
            services.AddTransient<ISpatialIndexService, SpatialIndexService>();

            // Analysis services
            services.AddTransient<ITrayAnalysisService, TrayAnalysisService>();
            services.AddTransient<ICableDetectionService, CableDetectionService>();

            // Data loading services
            services.AddTransient<IDataLoadingService, DataLoadingService>();

            // UI services
            services.AddTransient<ITaggingService, TaggingService>();
            services.AddTransient<ISectionViewService, SectionViewService>();
            services.AddTransient<IParametricCableService, ParametricCableService>();
            services.AddTransient<ICableVisualizationService, CableVisualizationService>();

        }

        /// <summary>
        /// Register all ViewModels
        /// </summary>
        /// <param name="services">Service collection</param>
        private static void RegisterViewModels(IServiceCollection services)
        {
            // Main ViewModels - Singleton to share data across pages
            services.AddSingleton<MainViewModel>();

            // Tray Tagger ViewModel - Transient for each window instance
            services.AddTransient<TrayTaggerViewModel>();

        }

        /// <summary>
        /// Build and configure the service provider
        /// </summary>
        /// <param name="uiDocument">Current UI document</param>
        /// <param name="logger">Activity logger</param>
        /// <returns>Configured service provider</returns>
        public static IServiceProvider BuildServiceProvider(UIDocument uiDocument, BecaActivityLoggerData logger)
        {
            var services = new ServiceCollection();
            services.ConfigureServices(uiDocument, logger);
            return services.BuildServiceProvider();
        }
    }
}
