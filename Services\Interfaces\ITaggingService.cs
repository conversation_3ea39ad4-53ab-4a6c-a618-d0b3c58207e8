using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Electrical;
using MEP.TraylorSwift.Models;
using System.Collections.Generic;
using View = Autodesk.Revit.DB.View;

namespace MEP.TraylorSwift.Services.Interfaces
{
    /// <summary>
    /// Service for creating and managing cable tray tags in Revit
    /// </summary>
    public interface ITaggingService
    {
        #region Tag Creation

        /// <summary>
        /// Create a tag for a cable tray
        /// </summary>
        /// <param name="cableTray">Cable tray to tag</param>
        /// <param name="traySegment">Tray segment data for tag content</param>
        /// <returns>Created tag element or null if creation failed</returns>
        IndependentTag CreateTrayTag(CableTray cableTray, CableTraySegmentModel traySegment);

        /// <summary>
        /// Create tags for multiple cable trays
        /// </summary>
        /// <param name="traySegments">Tray segments to tag</param>
        /// <param name="options">Tagging options</param>
        /// <returns>List of created tag elements</returns>
        List<IndependentTag> CreateMultipleTrayTags(List<CableTraySegmentModel> traySegments, TaggingOptions options);

        /// <summary>
        /// Create a tag at a specific position
        /// </summary>
        /// <param name="cableTray">Cable tray to tag</param>
        /// <param name="position">Tag position</param>
        /// <param name="orientation">Tag orientation</param>
        /// <returns>Created tag element or null if creation failed</returns>
        IndependentTag CreateTagAtPosition(CableTray cableTray, XYZ position, TagOrientation orientation);

        #endregion

        #region Tag Management

        /// <summary>
        /// Update tag content with latest cable information
        /// </summary>
        /// <param name="tag">Tag to update</param>
        /// <param name="traySegment">Updated tray segment data</param>
        /// <returns>True if tag was updated successfully</returns>
        bool UpdateTagContent(IndependentTag tag, CableTraySegmentModel traySegment);

        /// <summary>
        /// Remove a tag from the document
        /// </summary>
        /// <param name="tag">Tag to remove</param>
        /// <returns>True if tag was removed successfully</returns>
        bool RemoveTag(IndependentTag tag);

        /// <summary>
        /// Find existing tags for a cable tray
        /// </summary>
        /// <param name="cableTray">Cable tray to find tags for</param>
        /// <returns>List of existing tags</returns>
        List<IndependentTag> FindExistingTags(CableTray cableTray);

        /// <summary>
        /// Check if a cable tray already has a tag
        /// </summary>
        /// <param name="cableTray">Cable tray to check</param>
        /// <returns>True if tray has existing tag</returns>
        bool HasExistingTag(CableTray cableTray);

        #endregion

        #region Tag Positioning

        /// <summary>
        /// Calculate optimal tag position for a cable tray
        /// </summary>
        /// <param name="cableTray">Cable tray to position tag for</param>
        /// <param name="view">View where tag will be placed</param>
        /// <returns>Optimal tag position</returns>
        XYZ CalculateOptimalTagPosition(CableTray cableTray, View view);

        /// <summary>
        /// Adjust tag position to avoid overlaps
        /// </summary>
        /// <param name="proposedPosition">Proposed tag position</param>
        /// <param name="view">View containing the tag</param>
        /// <param name="avoidanceRadius">Radius for overlap avoidance</param>
        /// <returns>Adjusted position</returns>
        XYZ AdjustTagPositionForOverlaps(XYZ proposedPosition, View view, double avoidanceRadius);

        /// <summary>
        /// Get optimal tag positions for multiple trays
        /// </summary>
        /// <param name="cableTrays">Cable trays to position tags for</param>
        /// <param name="view">View where tags will be placed</param>
        /// <param name="options">Tag positioning options</param>
        /// <returns>Dictionary of tray to position mappings</returns>
        Dictionary<CableTray, XYZ> GetOptimalTagPositions(List<CableTray> cableTrays, View view, TagPositioningOptions options);

        #endregion

        #region Tag Types and Families

        /// <summary>
        /// Get or create a cable tray tag type
        /// </summary>
        /// <returns>Cable tray tag type or null if not available</returns>
        FamilySymbol GetOrCreateCableTrayTagType();

        /// <summary>
        /// Load a tag family from file
        /// </summary>
        /// <param name="familyPath">Path to tag family file</param>
        /// <returns>True if family was loaded successfully</returns>
        bool LoadTagFamily(string familyPath);

        #endregion

        #region Validation

        /// <summary>
        /// Validate tag creation requirements
        /// </summary>
        /// <param name="cableTray">Cable tray to validate</param>
        /// <param name="view">View for tag creation</param>
        /// <returns>List of validation issues</returns>
        List<string> ValidateTagCreationRequirements(CableTray cableTray, View view);

        /// <summary>
        /// Check if view supports tagging
        /// </summary>
        /// <param name="view">View to check</param>
        /// <returns>True if view supports tagging</returns>
        bool IsViewSuitableForTagging(View view);

        #endregion
    }

}
